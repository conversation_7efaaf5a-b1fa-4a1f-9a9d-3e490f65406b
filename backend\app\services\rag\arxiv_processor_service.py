# @GeneratedBy:AI
"""
arXiv论文处理服务 - 简化版本
"""

import asyncio
import logging
import uuid
import tempfile
from typing import List, Dict, Optional, Any
from datetime import datetime

from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from app.core.di.containers import Container
from app.models.rag.arxiv import ArxivProcessingStatusModel
from app.utils.rag_utils.nas_arxiv_utils import NASArxivUtil, ArxivChunk
from app.utils.rag_utils.nas_utils import NASUtil
from app.services.vector_store.manager import VectorStoreManager
from app.services.embedder.manager import EmbedderManager
from app.services.vector_store.models import VectorDocument
from app.schemas.rag.arxiv import (
    ArxivProcessRequest, ArxivProcessResponse, 
    ArxivSearchRequest, ArxivSearchResponse,
    ArxivChunkInfo
)

logger = logging.getLogger(__name__)


class ArxivProcessorService:
    """arXiv论文处理服务"""
    
    def __init__(self, container: Container = None):
        """
        初始化arXiv处理服务
        
        Args:
            container: 依赖注入容器
        """
        self.container = container or Container()
        self.nas_util = None
        self.vector_manager = None
        self.embedder_manager = None
        
        # NAS连接配置
        self.nas_config = {
            'username': "wzc",
            'password': "Helloshiyu123.",
            'client_name': "python_client",
            'server_name': "NAS",
            'server_ip': "***********",
            'port': 445
        }
        
        # 共享文件夹配置
        self.share_config = {
            'share_name': "NetBackup",
            'remote_path': "/arxiv/arxiv/files/src",
            'nas_ip': "***********",
            'nas_device_id': "NAS_001"
        }
    
    async def initialize(self) -> bool:
        """初始化服务"""
        try:
            # 初始化向量存储管理器
            self.vector_manager = VectorStoreManager()
            await self.vector_manager.initialize()
            
            # 初始化嵌入器管理器
            self.embedder_manager = EmbedderManager()
            await self.embedder_manager.initialize()
            
            logger.info("arXiv处理服务初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"arXiv处理服务初始化失败: {e}")
            return False
    
    def _connect_to_nas(self) -> bool:
        """连接到NAS服务器"""
        try:
            self.nas_util = NASUtil(**self.nas_config)
            if self.nas_util.connect():
                logger.info("成功连接到NAS服务器")
                return True
            else:
                logger.error("连接NAS服务器失败")
                return False
        except Exception as e:
            logger.error(f"连接NAS服务器时发生错误: {e}")
            return False
    
    def _disconnect_nas(self):
        """断开NAS连接"""
        if self.nas_util:
            self.nas_util.disconnect()
    
    async def process_arxiv_files(self, request: ArxivProcessRequest) -> ArxivProcessResponse:
        """
        处理arXiv文件
        
        Args:
            request: 处理请求
            
        Returns:
            ArxivProcessResponse: 处理响应
        """
        task_id = str(uuid.uuid4())
        logger.info(f"开始处理arXiv文件，任务ID: {task_id}")
        
        try:
            # 连接NAS
            if not self._connect_to_nas():
                return ArxivProcessResponse(
                    task_id=task_id,
                    status="error",
                    message="无法连接到NAS服务器"
                )
            
            # 获取待处理文件列表
            pending_files = await self._get_pending_files(request.max_files)
            if not pending_files:
                return ArxivProcessResponse(
                    task_id=task_id,
                    status="completed",
                    message="没有待处理的文件"
                )
            
            # 处理文件
            total_chunks = 0
            total_vectors = 0
            processed_files = 0
            
            for file_info in pending_files:
                try:
                    result = await self._process_single_file(file_info, request, task_id)
                    
                    if result is not None:
                        chunks_count, vectors_count = result
                        total_chunks += chunks_count
                        total_vectors += vectors_count
                        processed_files += 1
                        
                        # 更新数据库状态
                        await self._update_file_status(
                            file_info['id'], 
                            'completed',
                            vectors_count
                        )
                        
                        logger.info(f"文件处理完成: {file_info['file_name']}")
                    
                except Exception as e:
                    logger.error(f"处理文件 {file_info['file_name']} 时发生错误: {e}")
                    await self._update_file_status(
                        file_info['id'], 
                        'error',
                        error_message=str(e)
                    )
            
            return ArxivProcessResponse(
                task_id=task_id,
                status="completed",
                message=f"处理完成，共处理 {processed_files} 个文件",
                processed_files=processed_files,
                total_papers=0,  # 不再统计单篇论文数量
                total_chunks=total_chunks,
                total_vectors=total_vectors
            )
            
        except Exception as e:
            logger.error(f"处理arXiv文件时发生错误: {e}")
            return ArxivProcessResponse(
                task_id=task_id,
                status="error",
                message=f"处理失败: {str(e)}"
            )
        
        finally:
            self._disconnect_nas()
    
    async def _get_pending_files(self, max_files: int) -> List[Dict]:
        """获取待处理文件列表"""
        try:
            session_provider = self.container.session_provider()
            with session_provider() as session:
                files = session.query(ArxivProcessingStatusModel).filter(
                    ArxivProcessingStatusModel.processing_status == 'pending'
                ).limit(max_files).all()
                
                return [
                    {
                        'id': f.id,
                        'file_name': f.file_name,
                        'file_path': f.file_path,
                        'max_papers_limit': f.max_papers_limit
                    }
                    for f in files
                ]
                
        except Exception as e:
            logger.error(f"获取待处理文件列表时发生错误: {e}")
            return []
    
    async def _process_single_file(
        self, file_info: Dict, request: ArxivProcessRequest, task_id: str
    ) -> Optional[tuple[int, int]]:
        """
        处理单个文件
        
        Returns:
            Tuple[chunks_count, vectors_count] 或 None
        """
        temp_file_path = None
        
        try:
            # 下载文件到临时目录
            temp_file_path = self.nas_util.download_file_to_temp(
                self.share_config['share_name'],
                file_info['file_path']
            )
            
            if not temp_file_path:
                logger.error(f"下载文件失败: {file_info['file_name']}")
                return None
            
            # 使用工具类处理压缩文件，获取分段数据
            arxiv_util = NASArxivUtil()
            chunks = arxiv_util.get_data(
                temp_file_path,
                max_papers=file_info['max_papers_limit'],
                chunk_size=request.chunk_size,
                chunk_overlap=request.chunk_overlap
            )
            
            if not chunks:
                logger.warning(f"文件中没有提取到内容: {file_info['file_name']}")
                return 0, 0
            
            # 处理分段并存储到向量数据库
            vectors_created = 0
            
            for chunk in chunks:
                try:
                    # 创建向量文档并存储
                    if await self._store_chunk_to_vector_db(chunk, request):
                        vectors_created += 1
                except Exception as e:
                    logger.error(f"存储分段 {chunk.chunk_id} 时发生错误: {e}")
                    continue
            
            return len(chunks), vectors_created
            
        except Exception as e:
            logger.error(f"处理文件时发生错误: {e}")
            return None
            
        finally:
            # 清理临时文件
            if temp_file_path and os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                except:
                    pass
    
    async def _store_chunk_to_vector_db(
        self, chunk: ArxivChunk, request: ArxivProcessRequest
    ) -> bool:
        """
        将分段存储到向量数据库
        
        Args:
            chunk: 分段对象
            request: 处理请求
            
        Returns:
            bool: 是否存储成功
        """
        try:
            # 获取嵌入器
            embedder = await self.embedder_manager.get_embedder(request.embedding_model)
            
            # 创建嵌入
            embedding_result = await embedder.embed_single(chunk.content)
            
            # 创建向量文档
            vector_doc = VectorDocument(
                id=chunk.chunk_id,
                content=chunk.content,
                embedding=embedding_result.embedding,
                metadata={
                    **chunk.metadata,
                    'paper_id': chunk.paper_id,
                    'chunk_type': chunk.chunk_type,
                    'chunk_index': chunk.chunk_index,
                    'embedding_model': request.embedding_model,
                    'created_at': datetime.now().isoformat()
                }
            )
            
            # 存储到向量数据库
            vector_store = await self.vector_manager.get_store(request.vector_store)
            result = await vector_store.add_documents([vector_doc])
            
            return result.get('success', False)
            
        except Exception as e:
            logger.error(f"存储分段到向量数据库时发生错误: {e}")
            return False
    
    async def _update_file_status(
        self, file_id: str, status: str, vectors_count: int = 0, error_message: str = None
    ):
        """更新文件处理状态"""
        try:
            session_provider = self.container.session_provider()
            with session_provider() as session:
                file_record = session.query(ArxivProcessingStatusModel).filter(
                    ArxivProcessingStatusModel.id == file_id
                ).first()
                
                if file_record:
                    file_record.processing_status = status
                    file_record.vectors_created = vectors_count
                    
                    if status == 'completed':
                        file_record.is_processed = True
                        file_record.processed_at = datetime.now()
                    elif status == 'error':
                        file_record.error_message = error_message
                    
                    session.commit()
                    
        except Exception as e:
            logger.error(f"更新文件状态时发生错误: {e}")
    
    async def search_papers(self, request: ArxivSearchRequest) -> ArxivSearchResponse:
        """
        搜索arXiv论文
        
        Args:
            request: 搜索请求
            
        Returns:
            ArxivSearchResponse: 搜索响应
        """
        start_time = datetime.now()
        
        try:
            # 获取向量存储
            vector_store = await self.vector_manager.get_store("milvus_quality")
            
            # 创建搜索查询
            from app.services.vector_store.models import VectorQuery
            query = VectorQuery(
                query_text=request.query,
                top_k=request.top_k,
                similarity_threshold=request.similarity_threshold,
                filters=request.filters or {}
            )
            
            # 执行搜索
            search_results = await vector_store.search(query)
            
            # 转换结果格式
            results = []
            for result in search_results.results:
                chunk_info = ArxivChunkInfo(
                    chunk_id=result.id,
                    paper_id=result.metadata.get('paper_id', ''),
                    content=result.content,
                    chunk_index=result.metadata.get('chunk_index', 0),
                    chunk_type=result.metadata.get('chunk_type', 'content'),
                    metadata=result.metadata
                )
                results.append(chunk_info)
            
            search_time = (datetime.now() - start_time).total_seconds()
            
            return ArxivSearchResponse(
                query=request.query,
                results=results,
                total_results=len(results),
                search_time=search_time,
                metadata={
                    'vector_store': 'milvus_quality',
                    'search_type': request.search_type
                }
            )
            
        except Exception as e:
            logger.error(f"搜索论文时发生错误: {e}")
            return ArxivSearchResponse(
                query=request.query,
                results=[],
                total_results=0,
                search_time=0.0,
                metadata={'error': str(e)}
            )
    
    async def get_processing_status(self) -> Dict[str, Any]:
        """获取处理状态统计"""
        try:
            session_provider = self.container.session_provider()
            with session_provider() as session:
                # 统计文件处理状态
                total_files = session.query(ArxivProcessingStatusModel).count()
                processed_files = session.query(ArxivProcessingStatusModel).filter(
                    ArxivProcessingStatusModel.is_processed == True
                ).count()
                pending_files = session.query(ArxivProcessingStatusModel).filter(
                    ArxivProcessingStatusModel.processing_status == 'pending'
                ).count()
                error_files = session.query(ArxivProcessingStatusModel).filter(
                    ArxivProcessingStatusModel.processing_status == 'error'
                ).count()
                
                # 统计向量数量
                total_vectors = session.query(ArxivProcessingStatusModel).with_entities(
                    ArxivProcessingStatusModel.vectors_created
                ).all()
                total_vectors_count = sum(v[0] or 0 for v in total_vectors)
                
                return {
                    'total_files': total_files,
                    'processed_files': processed_files,
                    'pending_files': pending_files,
                    'error_files': error_files,
                    'processing_rate': round(processed_files / total_files * 100, 2) if total_files > 0 else 0,
                    'total_vectors': total_vectors_count
                }
                
        except Exception as e:
            logger.error(f"获取处理状态时发生错误: {e}")
            return {}

# arxiv_util = NASArxivUtil()
# chunks = arxiv_util.get_data("xxx-1.tar.gz", max_papers=100, chunk_size=1000)


