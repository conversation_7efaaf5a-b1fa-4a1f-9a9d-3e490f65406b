"""
进程操作工具处理器
"""

import logging
from typing import Dict, Any, List, Type, Optional
from pydantic import BaseModel, Field

from ...models.context import ExecutionContext
from ..base_handler import BaseToolHandler

logger = logging.getLogger(__name__)


class ProcessInput(BaseModel):
    """进程执行工具输入参数"""
    command: str = Field(description="要执行的shell命令")
    cwd: str = Field(description="工作目录的绝对路径")
    wait: bool = Field(description="是否等待命令完成")
    max_wait_seconds: Optional[float] = Field(description="等待命令完成的最大秒数", default=600)


class ProcessHandler(BaseToolHandler):
    """进程执行工具处理器"""

    def __init__(self):
        super().__init__("launch-process")

    def get_args_schema(self) -> Type[BaseModel]:
        """获取参数模型"""
        return ProcessInput

    async def execute(self, parameters: Dict[str, Any], context: ExecutionContext) -> Any:
        """执行进程"""
        command = parameters.get('command', '')
        cwd = parameters.get('cwd', context.workspace_path or '.')
        wait = parameters.get('wait', True)
        max_wait_seconds = parameters.get('max_wait_seconds', 60)

        logger.info(f"执行命令: {command} (工作目录: {cwd})")

        # 这里应该调用实际的 launch-process 工具
        # 暂时返回模拟结果
        return {
            "command": command,
            "cwd": cwd,
            "output": f"命令执行结果: {command}",
            "return_code": 0,
            "timestamp": context.current_time
        }

    def validate_parameters(self, parameters: Dict[str, Any]) -> List[str]:
        """验证参数"""
        errors = []
        if not parameters.get('command'):
            errors.append("缺少必需参数: command")
        if not parameters.get('cwd'):
            errors.append("缺少必需参数: cwd")
        return errors
