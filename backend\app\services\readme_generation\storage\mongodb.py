"""
MongoDB存储实现

基于MongoDB的README生成存储实现，支持逻辑树、节点内容和项目上下文的持久化存储
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone

from ....core.di.providers.mongodb import AsyncMongoProvider
from .base import BaseStorage
from ..models.logic_tree import LogicTree
from ..models.content import NodeContent
from ..models.project_context import ProjectContext

logger = logging.getLogger(__name__)


class MongoDBStorage(BaseStorage):
    """MongoDB存储实现"""
    
    # 集合名称常量
    LOGIC_TREES_COLLECTION = "readme_logic_trees"
    NODE_CONTENTS_COLLECTION = "readme_node_contents"
    PROJECT_CONTEXTS_COLLECTION = "readme_project_contexts"
    
    def __init__(self, mongo_provider: AsyncMongoProvider):
        """初始化MongoDB存储
        
        Args:
            mongo_provider: MongoDB异步提供者
        """
        self.mongo_provider = mongo_provider
        self._initialized = False
    
    async def initialize(self) -> bool:
        """初始化存储"""
        try:
            # 创建索引以提高查询性能
            await self._create_indexes()
            self._initialized = True
            logger.info("MongoDB存储初始化成功")
            return True
        except Exception as e:
            logger.error(f"MongoDB存储初始化失败: {e}")
            return False
    
    async def shutdown(self) -> None:
        """关闭存储"""
        self._initialized = False
        logger.info("MongoDB存储已关闭")
    
    async def _create_indexes(self) -> None:
        """创建数据库索引"""
        try:
            # 逻辑树索引
            await self.mongo_provider.create_index(
                self.LOGIC_TREES_COLLECTION, 
                "project_id"
            )
            await self.mongo_provider.create_index(
                self.LOGIC_TREES_COLLECTION, 
                "id", 
                unique=True
            )
            
            # 节点内容索引
            await self.mongo_provider.create_index(
                self.NODE_CONTENTS_COLLECTION, 
                "node_id"
            )
            await self.mongo_provider.create_index(
                self.NODE_CONTENTS_COLLECTION, 
                "tree_id"
            )
            await self.mongo_provider.create_index(
                self.NODE_CONTENTS_COLLECTION, 
                "id", 
                unique=True
            )
            
            # 项目上下文索引
            await self.mongo_provider.create_index(
                self.PROJECT_CONTEXTS_COLLECTION, 
                "project_id", 
                unique=True
            )
            
            logger.info("MongoDB索引创建成功")
        except Exception as e:
            logger.error(f"MongoDB索引创建失败: {e}")
            raise
    
    def _model_to_dict(self, model) -> Dict[str, Any]:
        """将Pydantic模型转换为字典"""
        data = model.model_dump()
        # 处理datetime字段
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()
        return data
    
    def _dict_to_logic_tree(self, data: Dict[str, Any]) -> LogicTree:
        """将字典转换为LogicTree模型"""
        # 处理datetime字段
        if 'created_at' in data and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data and isinstance(data['updated_at'], str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        # 移除MongoDB的_id字段
        data.pop('_id', None)
        return LogicTree(**data)
    
    def _dict_to_node_content(self, data: Dict[str, Any]) -> NodeContent:
        """将字典转换为NodeContent模型"""
        # 处理datetime字段
        if 'created_at' in data and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data and isinstance(data['updated_at'], str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        # 处理versions中的datetime字段
        if 'versions' in data:
            for version in data['versions']:
                if 'created_at' in version and isinstance(version['created_at'], str):
                    version['created_at'] = datetime.fromisoformat(version['created_at'])
        
        # 移除MongoDB的_id字段
        data.pop('_id', None)
        return NodeContent(**data)
    
    def _dict_to_project_context(self, data: Dict[str, Any]) -> ProjectContext:
        """将字典转换为ProjectContext模型"""
        # 处理datetime字段
        if 'created_at' in data and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data and isinstance(data['updated_at'], str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        # 移除MongoDB的_id字段
        data.pop('_id', None)
        return ProjectContext(**data)
    
    # 逻辑树存储
    async def save_logic_tree(self, tree: LogicTree) -> bool:
        """保存逻辑树"""
        try:
            tree_data = self._model_to_dict(tree)
            
            # 使用upsert操作，如果存在则更新，不存在则插入
            success = await self.mongo_provider.update_one(
                self.LOGIC_TREES_COLLECTION,
                {"id": tree.id},
                {"$set": tree_data},
                upsert=True
            )
            
            if success:
                logger.info(f"逻辑树保存成功: {tree.id}")
            else:
                logger.error(f"逻辑树保存失败: {tree.id}")
            
            return success
        except Exception as e:
            logger.error(f"保存逻辑树异常 {tree.id}: {e}")
            return False
    
    async def get_logic_tree(self, tree_id: str) -> Optional[LogicTree]:
        """获取逻辑树"""
        try:
            data = await self.mongo_provider.find_one(
                self.LOGIC_TREES_COLLECTION,
                {"id": tree_id}
            )
            
            if data:
                return self._dict_to_logic_tree(data)
            return None
        except Exception as e:
            logger.error(f"获取逻辑树异常 {tree_id}: {e}")
            return None
    
    async def get_logic_trees_by_project(self, project_id: str) -> List[LogicTree]:
        """根据项目ID获取逻辑树列表"""
        try:
            documents = await self.mongo_provider.find_many(
                self.LOGIC_TREES_COLLECTION,
                {"project_id": project_id},
                sort=[("created_at", -1)]  # 按创建时间倒序
            )
            
            trees = []
            for doc in documents:
                try:
                    tree = self._dict_to_logic_tree(doc)
                    trees.append(tree)
                except Exception as e:
                    logger.error(f"转换逻辑树数据失败: {e}")
                    continue
            
            return trees
        except Exception as e:
            logger.error(f"获取项目逻辑树列表异常 {project_id}: {e}")
            return []

    async def get_all_logic_trees(self) -> List[LogicTree]:
        """获取全部逻辑树"""
        try:
            documents = await self.mongo_provider.find_many(
                self.LOGIC_TREES_COLLECTION,
                {},  # 空查询条件，获取所有文档
                sort=[("created_at", -1)]  # 按创建时间倒序
            )

            trees = []
            for doc in documents:
                try:
                    tree = self._dict_to_logic_tree(doc)
                    trees.append(tree)
                except Exception as e:
                    logger.error(f"转换逻辑树数据失败: {e}")
                    continue

            logger.info(f"获取全部逻辑树成功，共 {len(trees)} 个")
            return trees
        except Exception as e:
            logger.error(f"获取全部逻辑树异常: {e}")
            return []

    async def delete_logic_tree(self, tree_id: str) -> bool:
        """删除逻辑树"""
        try:
            # 先删除相关的节点内容
            await self.mongo_provider.delete_many(
                self.NODE_CONTENTS_COLLECTION,
                {"tree_id": tree_id}
            )
            
            # 删除逻辑树
            success = await self.mongo_provider.delete_one(
                self.LOGIC_TREES_COLLECTION,
                {"id": tree_id}
            )
            
            if success:
                logger.info(f"逻辑树删除成功: {tree_id}")
            else:
                logger.warning(f"逻辑树不存在或删除失败: {tree_id}")
            
            return success
        except Exception as e:
            logger.error(f"删除逻辑树异常 {tree_id}: {e}")
            return False
    
    # 节点内容存储
    async def save_node_content(self, content: NodeContent) -> bool:
        """保存节点内容"""
        try:
            content_data = self._model_to_dict(content)
            
            # 使用upsert操作
            success = await self.mongo_provider.update_one(
                self.NODE_CONTENTS_COLLECTION,
                {"id": content.id},
                {"$set": content_data},
                upsert=True
            )
            
            if success:
                logger.info(f"节点内容保存成功: {content.id}")
            else:
                logger.error(f"节点内容保存失败: {content.id}")
            
            return success
        except Exception as e:
            logger.error(f"保存节点内容异常 {content.id}: {e}")
            return False
    
    async def get_node_content(self, content_id: str) -> Optional[NodeContent]:
        """获取节点内容"""
        try:
            data = await self.mongo_provider.find_one(
                self.NODE_CONTENTS_COLLECTION,
                {"id": content_id}
            )
            
            if data:
                return self._dict_to_node_content(data)
            return None
        except Exception as e:
            logger.error(f"获取节点内容异常 {content_id}: {e}")
            return None
    
    async def get_node_content_by_node(self, node_id: str) -> Optional[NodeContent]:
        """根据节点ID获取内容"""
        try:
            data = await self.mongo_provider.find_one(
                self.NODE_CONTENTS_COLLECTION,
                {"node_id": node_id}
            )
            
            if data:
                return self._dict_to_node_content(data)
            return None
        except Exception as e:
            logger.error(f"根据节点ID获取内容异常 {node_id}: {e}")
            return None
    
    async def get_node_contents_by_tree(self, tree_id: str) -> List[NodeContent]:
        """根据逻辑树ID获取所有节点内容"""
        try:
            documents = await self.mongo_provider.find_many(
                self.NODE_CONTENTS_COLLECTION,
                {"tree_id": tree_id},
                sort=[("created_at", -1)]
            )
            
            contents = []
            for doc in documents:
                try:
                    content = self._dict_to_node_content(doc)
                    contents.append(content)
                except Exception as e:
                    logger.error(f"转换节点内容数据失败: {e}")
                    continue
            
            return contents
        except Exception as e:
            logger.error(f"获取树节点内容列表异常 {tree_id}: {e}")
            return []
    
    async def delete_node_content(self, content_id: str) -> bool:
        """删除节点内容"""
        try:
            success = await self.mongo_provider.delete_one(
                self.NODE_CONTENTS_COLLECTION,
                {"id": content_id}
            )
            
            if success:
                logger.info(f"节点内容删除成功: {content_id}")
            else:
                logger.warning(f"节点内容不存在或删除失败: {content_id}")
            
            return success
        except Exception as e:
            logger.error(f"删除节点内容异常 {content_id}: {e}")
            return False

    # 项目上下文存储
    async def save_project_context(self, context: ProjectContext) -> bool:
        """保存项目上下文"""
        try:
            context_data = self._model_to_dict(context)

            # 使用upsert操作
            success = await self.mongo_provider.update_one(
                self.PROJECT_CONTEXTS_COLLECTION,
                {"project_id": context.project_id},
                {"$set": context_data},
                upsert=True
            )

            if success:
                logger.info(f"项目上下文保存成功: {context.project_id}")
            else:
                logger.error(f"项目上下文保存失败: {context.project_id}")

            return success
        except Exception as e:
            logger.error(f"保存项目上下文异常 {context.project_id}: {e}")
            return False

    async def get_project_context(self, project_id: str) -> Optional[ProjectContext]:
        """获取项目上下文"""
        try:
            data = await self.mongo_provider.find_one(
                self.PROJECT_CONTEXTS_COLLECTION,
                {"project_id": project_id}
            )

            if data:
                return self._dict_to_project_context(data)
            return None
        except Exception as e:
            logger.error(f"获取项目上下文异常 {project_id}: {e}")
            return None

    async def delete_project_context(self, project_id: str) -> bool:
        """删除项目上下文"""
        try:
            success = await self.mongo_provider.delete_one(
                self.PROJECT_CONTEXTS_COLLECTION,
                {"project_id": project_id}
            )

            if success:
                logger.info(f"项目上下文删除成功: {project_id}")
            else:
                logger.warning(f"项目上下文不存在或删除失败: {project_id}")

            return success
        except Exception as e:
            logger.error(f"删除项目上下文异常 {project_id}: {e}")
            return False

    # 查询和统计
    async def list_projects(self) -> List[str]:
        """列出所有项目ID"""
        try:
            documents = await self.mongo_provider.find_many(
                self.PROJECT_CONTEXTS_COLLECTION,
                {},
                projection={"project_id": 1}
            )

            project_ids = [doc["project_id"] for doc in documents if "project_id" in doc]
            return project_ids
        except Exception as e:
            logger.error(f"列出项目ID异常: {e}")
            return []

    async def get_storage_statistics(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        try:
            logic_trees_count = await self.mongo_provider.count_documents(
                self.LOGIC_TREES_COLLECTION
            )
            node_contents_count = await self.mongo_provider.count_documents(
                self.NODE_CONTENTS_COLLECTION
            )
            project_contexts_count = await self.mongo_provider.count_documents(
                self.PROJECT_CONTEXTS_COLLECTION
            )

            # 获取项目统计
            project_stats = await self.mongo_provider.find_many(
                self.LOGIC_TREES_COLLECTION,
                {},
                projection={"project_id": 1}
            )
            unique_projects = len(set(doc["project_id"] for doc in project_stats if "project_id" in doc))

            return {
                "type": "mongodb",
                "initialized": self._initialized,
                "logic_trees_count": logic_trees_count,
                "node_contents_count": node_contents_count,
                "project_contexts_count": project_contexts_count,
                "projects_count": unique_projects,
                "collections": {
                    "logic_trees": self.LOGIC_TREES_COLLECTION,
                    "node_contents": self.NODE_CONTENTS_COLLECTION,
                    "project_contexts": self.PROJECT_CONTEXTS_COLLECTION
                },
                "timestamp": datetime.now(timezone.utc)
            }
        except Exception as e:
            logger.error(f"获取存储统计信息异常: {e}")
            return {
                "type": "mongodb",
                "initialized": self._initialized,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc)
            }

    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self._initialized:
                return False

            # 尝试执行一个简单的查询来检查连接
            await self.mongo_provider.count_documents(self.LOGIC_TREES_COLLECTION)
            return True
        except Exception as e:
            logger.error(f"MongoDB健康检查失败: {e}")
            return False
