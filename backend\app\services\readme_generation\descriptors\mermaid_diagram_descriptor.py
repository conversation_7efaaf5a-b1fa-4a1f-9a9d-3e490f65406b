"""
Mermaid架构图生成描述器

基于项目代码结构和架构分析，生成Mermaid格式的架构图表
"""

import logging
from typing import List

from ...execution_framework.base.step_type import StepType
from ...execution_framework.models.context import ExecutionContext
from ...execution_framework.models.tool_instruction import ToolInstruction
from .base_generation_descriptor import BaseGenerationDescriptor

logger = logging.getLogger(__name__)


class MermaidDiagramDescriptor(BaseGenerationDescriptor):
    """Mermaid架构图生成描述器 - 指导LLM生成项目架构的可视化图表"""

    def __init__(self):
        """初始化Mermaid架构图生成描述器"""
        super().__init__(StepType.MERMAID_DIAGRAM, "mermaid_diagram")

    def get_description(self) -> str:
        """获取描述器描述"""
        return "基于项目代码结构和架构分析，生成Mermaid格式的架构图表，包括系统组件图、数据流图、部署图等"

    def get_tool_instructions(self, context: ExecutionContext) -> List[ToolInstruction]:
        """获取工具调用指令列表 - 每个工具只定义一次"""
        return [
            ToolInstruction(
                name="codebase-retrieval",
                parameters={
                    "information_request": "项目架构设计、模块划分、组件关系、数据流、技术栈、部署架构"
                },
                description="获取项目架构和组件相关信息",
                result_key="codebase_analysis"
            ),
            ToolInstruction(
                name="view-directory",
                parameters={"path": "."},
                description="查看项目目录结构（可多次调用查看不同目录）",
                result_key="directory_structure"
            ),
            ToolInstruction(
                name="view-file",
                parameters={"path": "architecture.md"},
                description="查看架构相关文档（可多次调用查看不同文件）",
                result_key="architecture_docs"
            )
        ]

    def get_prompt_template(self) -> str:
        """获取提示词模板 - 指导LLM如何生成Mermaid架构图"""
        return """
## 项目信息
项目名称: {project_name}
项目路径: {project_path}
项目类型: {project_type}
编程语言: {programming_languages}
技术框架: {frameworks}

## 代码库分析
{codebase_analysis}

## 目录结构
{directory_structure}

## 架构文档
{architecture_docs}

## 自定义要求
{custom_prompt}

## 生成任务

请基于以上项目信息，使用可用工具深入分析后，生成多种类型的Mermaid架构图。

### 工具使用指导：
1. 使用 codebase-retrieval 获取项目架构设计和组件关系
2. 使用 view-directory 查看多个关键目录：
   - src/（源代码结构）
   - app/（应用程序结构）
   - services/（服务层结构）
   - components/（组件结构）
   - modules/（模块结构）
3. 使用 view-file 查看多个架构相关文档：
   - architecture.md（架构文档）
   - README.md（项目说明）
   - docker-compose.yml（部署架构）
   - package.json（依赖关系）

### 重点包含：
1. **系统架构图** (graph TD)
   - 主要模块和组件
   - 模块间的依赖关系
   - 数据流向

2. **组件关系图** (graph LR)
   - 核心组件
   - 组件间的交互
   - 接口关系

3. **数据流图** (flowchart TD)
   - 数据处理流程
   - 输入输出关系
   - 处理步骤

4. **部署架构图** (graph TB)
   - 部署环境
   - 服务分布
   - 网络关系

### Mermaid语法要求：
- 使用标准的Mermaid语法
- 图表类型：graph TD/LR/TB, flowchart, sequenceDiagram, classDiagram
- 节点命名清晰，使用有意义的标识符
- 添加适当的样式和颜色区分
- 包含图表标题和说明

### 输出格式：
```mermaid
graph TD
    A[组件A] --> B[组件B]
    B --> C[组件C]
    
    classDef primary fill:#e1f5fe
    classDef secondary fill:#f3e5f5
    
    class A,B primary
    class C secondary
```

每个图表前后都要有适当的文字说明。
"""

    def get_input_variables(self) -> List[str]:
        """获取输入变量列表"""
        return [
            "project_name",
            "project_path",
            "project_type",
            "programming_languages",
            "frameworks",
            "codebase_analysis",
            "directory_structure",
            "architecture_docs",
            "custom_prompt"
        ]

    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return "你是一个专业的系统架构师和图表设计专家，擅长使用Mermaid语法创建清晰、准确的架构图表。"

    def get_constraints(self) -> List[str]:
        """将写作要求迁移为约束条件"""
        return [
            "使用标准的Mermaid语法",
            "生成多种类型的架构图（系统架构、组件关系、数据流、部署架构）",
            "图表必须基于实际代码分析和项目结构",
            "节点命名清晰，关系准确",
            "添加适当的样式和颜色区分",
            "每个图表包含标题和说明文字",
            "确保图表的可读性和专业性",
            "总长度控制在1000-1500字（包含图表代码）",
        ]

    def get_priority(self, context: ExecutionContext) -> int:
        """获取优先级 - 在架构说明之后生成"""
        return 65

    def get_dependencies(self) -> List[str]:
        """获取依赖 - 依赖架构说明"""
        return ["architecture"]
