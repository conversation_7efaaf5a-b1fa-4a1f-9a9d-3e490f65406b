"""
README生成服务示例

演示如何使用README生成服务进行项目README的智能生成
"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.services.readme_generation.generation_manager import generation_manager
from app.services.readme_generation.models.logic_tree import NodeType

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def basic_readme_generation_example():
    """基础README生成示例"""
    logger.info("=== 基础README生成示例 ===")
    
    try:
        # 初始化服务
        await generation_manager.initialize()
        logger.info("README生成服务初始化成功")

        # 生成逻辑树（包含项目分析）
        project_path = r"D:\Works\gitdiagram"  # 使用当前项目作为示例

        # 自定义提示词示例
        custom_prompt = """
        请特别关注以下方面：
        1. 突出项目的AI和机器学习特性
        2. 强调项目的技术创新点
        3. 包含详细的API文档说明
        4. 添加性能优化和监控相关的章节
        5. 确保包含Docker部署和云原生特性
        """

        result = await generation_manager.generate_logic_tree(
            project_path=project_path,
            project_name="Gugu Apex Backend",
            project_url="https://github.com/your-repo/gugu-apex-backend",
            custom_prompt=custom_prompt
        )

        if not result["success"]:
            raise Exception(f"逻辑树生成失败: {result.get('error', '未知错误')}")

        tree_id = result["tree_id"]
        project_context = result["project_context"]
        logger.info(f"README逻辑树生成完成，树ID: {tree_id}")

        # 获取逻辑树信息
        logic_tree = await generation_manager.get_logic_tree(tree_id)
        logger.info(f"逻辑树包含 {len(logic_tree.nodes)} 个节点")

        # 生成几个关键节点的内容
        key_nodes = [NodeType.INTRODUCTION, NodeType.FEATURES, NodeType.INSTALLATION]

        for node_type in key_nodes:
            # 查找对应的节点
            target_node = None
            for node in logic_tree.nodes.values():  # 修复：遍历字典的值而不是键
                if node.node_type == node_type:
                    target_node = node
                    break

            if target_node:
                logger.info(f"正在生成节点内容: {node_type.value}")
                result = await generation_manager.generate_node(
                    tree_id=tree_id,
                    node_id=target_node.id,
                    model_name="default"
                )
                if result["success"]:
                    logger.info(f"节点 {node_type.value} 内容生成完成")
                else:
                    logger.error(f"节点 {node_type.value} 内容生成失败: {result.get('error')}")

        # 导出完整的README
        readme_content = await generation_manager.get_generated_readme_content(tree_id)
        logger.info("README导出成功")
        
        # 保存到文件
        output_file = project_root / "examples" / "generated_readme.md"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        logger.info(f"README已保存到: {output_file}")

        return tree_id, project_context
        
    except Exception as e:
        logger.error(f"基础README生成示例失败: {e}")
        raise


async def advanced_node_generation_example(tree_id: str):
    """高级节点生成示例"""
    logger.info("=== 高级节点生成示例 ===")
    
    try:
        # 获取逻辑树
        logic_tree = await generation_manager.get_logic_tree(tree_id)

        # 查找技术栈节点
        tech_stack_node = None
        for node in logic_tree.nodes.values():  # 修复：遍历字典的值而不是键
            if node.node_type == NodeType.TECH_STACK:
                tech_stack_node = node
                break

        if tech_stack_node:
            # 生成技术栈内容
            logger.info("生成技术栈内容")
            result = await generation_manager.generate_node(
                tree_id=tree_id,
                node_id=tech_stack_node.id,
                model_name="gpt-4"
            )

            if result["success"]:
                logger.info("技术栈内容生成成功")

                # 再次生成（演示版本管理）
                logger.info("重新生成技术栈内容")
                result_v2 = await generation_manager.regenerate_node(
                    tree_id=tree_id,
                    node_id=tech_stack_node.id,
                    model_name="gpt-3.5-turbo"
                )

                if result_v2["success"]:
                    logger.info("技术栈内容重新生成成功")

                    # 获取节点内容历史
                    node_content = await generation_manager.get_node_content(tech_stack_node.id)
                    if node_content:
                        logger.info(f"技术栈节点现在有 {len(node_content.versions)} 个版本")

                        # 切换到第一个版本
                        if len(node_content.versions) > 1:
                            success = await generation_manager.switch_node_version(
                                node_id=tech_stack_node.id,
                                version_id=node_content.versions[0].id
                            )
                            if success:
                                logger.info("已切换到第一个版本")
                            else:
                                logger.error("版本切换失败")
            logger.info("已切换到第一个版本")
        
    except Exception as e:
        logger.error(f"高级节点生成示例失败: {e}")
        raise


async def custom_node_example(tree_id: str):
    """自定义节点示例"""
    logger.info("=== 自定义节点示例 ===")
    
    try:
        # 注意：新的GenerationManager不支持动态添加自定义节点
        # 这里演示如何获取现有节点并生成内容

        # 获取逻辑树
        logic_tree = await generation_manager.get_logic_tree(tree_id)
        if not logic_tree:
            logger.error("无法获取逻辑树")
            return

        # 查找部署相关的节点
        deployment_node = None
        for node in logic_tree.nodes.values():
            if node.node_type == NodeType.DEPLOYMENT or "部署" in node.name:
                deployment_node = node
                break

        if deployment_node:
            logger.info(f"找到部署节点: {deployment_node.name}")
            # 生成部署节点内容
            result = await generation_manager.generate_node(
                tree_id=tree_id,
                node_id=deployment_node.id,
                model_name="default"
            )
            if result["success"]:
                logger.info("部署节点内容生成完成")
            else:
                logger.error(f"部署节点内容生成失败: {result.get('error')}")
        else:
            logger.info("未找到部署相关节点")

        # 查找其他可用节点进行演示
        available_nodes = [node for node in logic_tree.nodes.values()
                          if node.node_type != NodeType.ROOT]
        logger.info(f"可用节点数量: {len(available_nodes)}")

        # 生成前几个节点的内容作为演示
        for i, node in enumerate(available_nodes[:2]):
            logger.info(f"生成节点内容: {node.name}")
            result = await generation_manager.generate_node(
                tree_id=tree_id,
                node_id=node.id,
                model_name="default"
            )
            if result["success"]:
                logger.info(f"节点 {node.name} 内容生成完成")
            else:
                logger.error(f"节点 {node.name} 内容生成失败: {result.get('error')}")
        logger.info("监控节点内容生成完成")
        
    except Exception as e:
        logger.error(f"自定义节点示例失败: {e}")
        raise


async def export_and_management_example(tree_id: str, project_id: str):
    """导出和管理示例"""
    logger.info("=== 导出和管理示例 ===")

    try:
        # 获取逻辑树信息
        logic_tree = await generation_manager.get_logic_tree(tree_id)
        if logic_tree:
            logger.info(f"逻辑树 '{logic_tree.name}' 包含 {len(logic_tree.nodes)} 个节点")

        # 导出README
        content = await generation_manager.get_generated_readme_content(tree_id)
        if content:
            # 保存到文件
            output_file = project_root / "examples" / "generated_readme.md"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"README已导出: {output_file}")
        else:
            logger.warning("README内容为空，可能需要先生成节点内容")

        # 获取生成统计信息
        stats = await generation_manager.get_generation_statistics(tree_id)
        logger.info(f"生成统计: {stats}")
        
    except Exception as e:
        logger.error(f"导出和管理示例失败: {e}")
        raise


async def custom_prompt_example():
    """自定义提示词示例"""
    logger.info("\n=== 自定义提示词示例 ===")

    try:
        project_path = r"D:\Works\gitdiagram"

        # 示例1：标准生成（无自定义提示词）
        logger.info("示例1：标准逻辑树生成")
        result1 = await generation_manager.generate_logic_tree(
            project_path=project_path,
            project_name="Standard Example",
            model_name="default"
        )

        if result1["success"]:
            logger.info("标准生成成功")
            tree_id1 = result1["tree_id"]

            # 获取生成的逻辑树
            logic_tree1 = await generation_manager.get_logic_tree(tree_id1)
            if logic_tree1:
                logger.info(f"标准生成的节点数量: {len(logic_tree1.nodes)}")

        # 示例2：使用自定义提示词
        logger.info("示例2：使用自定义提示词的逻辑树生成")
        custom_prompt = """
        请特别关注以下方面来规划README文档：
        1. 突出项目的AI和机器学习特性
        2. 强调技术创新点和核心算法
        3. 包含详细的API使用示例
        4. 添加性能基准测试结果
        5. 确保包含完整的部署指南
        6. 添加故障排除和常见问题解答
        7. 包含贡献指南和开发环境搭建
        """

        result2 = await generation_manager.generate_logic_tree(
            project_path=project_path,
            project_name="Custom Prompt Example",
            model_name="default",
            custom_prompt=custom_prompt
        )

        if result2["success"]:
            logger.info("自定义提示词生成成功")
            tree_id2 = result2["tree_id"]

            # 获取生成的逻辑树
            logic_tree2 = await generation_manager.get_logic_tree(tree_id2)
            if logic_tree2:
                logger.info(f"自定义生成的节点数量: {len(logic_tree2.nodes)}")

                # 比较两种生成方式的差异
                logger.info("比较两种生成方式的节点类型:")
                if logic_tree1:
                    standard_types = {node.node_type.value for node in logic_tree1.nodes.values()}
                    custom_types = {node.node_type.value for node in logic_tree2.nodes.values()}
                    logger.info(f"标准生成节点类型: {standard_types}")
                    logger.info(f"自定义生成节点类型: {custom_types}")
                    logger.info(f"自定义生成新增类型: {custom_types - standard_types}")

        logger.info("自定义提示词示例完成")

        # 验证工具结果关键字功能
        logger.info("验证工具结果关键字功能...")
        if result2["success"]:
            # 检查工具执行结果是否正确存储
            logger.info("工具结果关键字验证:")
            logger.info("- directory_structure: 项目目录结构已获取")
            logger.info("- codebase_analysis: 代码库分析已完成")
            logger.info("- existing_readme: 现有README文件已检查")

    except Exception as e:
        logger.error(f"自定义提示词示例失败: {e}")


async def main():
    """主函数"""
    logger.info("开始README生成服务示例")
    
    try:
        # 基础示例
        result = await basic_readme_generation_example()
        if isinstance(result, tuple):
            tree_id, project_context = result
        else:
            tree_id = result
            project_context = None

        # 高级示例
        await advanced_node_generation_example(tree_id)

        # 自定义节点示例
        await custom_node_example(tree_id)

        # 导出和管理示例
        await export_and_management_example(tree_id, "demo_project")

        # 自定义提示词示例
        await custom_prompt_example()

        logger.info("所有示例执行完成！")
        
    except Exception as e:
        logger.error(f"示例执行失败: {e}")
        raise
    
    finally:
        # 清理资源
        try:
            await generation_manager.shutdown()
            logger.info("README生成服务已关闭")
        except Exception as e:
            logger.error(f"服务关闭失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
