"""add nas-arxiv

Revision ID: 315af8ac9a49
Revises: 4ddfbe24ef9b
Create Date: 2025-09-16 14:54:46.478623

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '315af8ac9a49'
down_revision: Union[str, None] = '4ddfbe24ef9b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###

    # 创建arxiv_processing_status表
    op.create_table('arxiv_processing_status',
                    sa.Column('id', sa.VARCHAR(length=64), autoincrement=False, nullable=False, comment='主键ID'),
                    sa.Column('file_name', sa.VARCHAR(length=512), autoincrement=False, nullable=False,
                              comment='文件名'),
                    sa.Column('file_path', sa.VARCHAR(length=1024), autoincrement=False, nullable=False,
                              comment='文件路径'),
                    sa.Column('file_size', sa.INTEGER(), autoincrement=False, nullable=False, comment='文件大小(字节)'),
                    sa.Column('file_hash', sa.VARCHAR(length=64), autoincrement=False, nullable=False,
                              comment='文件MD5哈希'),
                    sa.Column('file_checksum', sa.VARCHAR(length=128), autoincrement=False, nullable=False,
                              comment='文件校验和(SHA256)'),
                    sa.Column('nas_ip', sa.VARCHAR(length=45), autoincrement=False, nullable=False,
                              comment='NAS设备IP地址'),
                    sa.Column('is_processed', sa.BOOLEAN(), autoincrement=False, nullable=False, server_default='false',
                              comment='是否已处理'),
                    sa.Column('processing_status', sa.VARCHAR(length=32), autoincrement=False, nullable=False,
                              server_default='pending', comment='处理状态'),
                    sa.Column('error_message', sa.TEXT(), autoincrement=False, nullable=True, comment='错误信息'),
                    sa.Column('error_type', sa.VARCHAR(length=64), autoincrement=False, nullable=True, comment='错误类型'),

                    sa.Column('papers_extracted', sa.INTEGER(), autoincrement=False, nullable=False, server_default='0',
                              comment='提取的论文数量'),
                    sa.Column('vectors_created', sa.INTEGER(), autoincrement=False, nullable=False, server_default='0',
                              comment='创建的向量数量'),
                    sa.Column('processed_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True,
                              comment='处理完成时间'),
                    sa.Column('max_papers_limit',  sa.INTEGER(), autoincrement=False, nullable=True,
                              comment='最大处理论文数'),
                    sa.Column('embedding_model', sa.VARCHAR(length=64), autoincrement=False, nullable=True,
                              comment='使用的嵌入模型'),

                    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False,
                              comment='创建时间'),
                    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False,
                              comment='更新时间'),
                    sa.Column('created_by', sa.VARCHAR(length=64), autoincrement=False, nullable=True,
                              comment='创建人ID'),
                    sa.Column('updated_by', sa.VARCHAR(length=64), autoincrement=False, nullable=True,
                              comment='更新人ID'),
                    sa.Column('version', sa.VARCHAR(length=64), autoincrement=False, nullable=True,
                              server_default='1.0.0', comment='版本号'),

                    sa.PrimaryKeyConstraint('id', name=op.f('arxiv_processing_status_pkey'))
                    )

    # 创建索引
    op.create_index('idx_arxiv_processing_file_path', 'arxiv_processing_status', ['file_path'])
    op.create_index('idx_arxiv_processing_file_hash', 'arxiv_processing_status', ['file_hash'])
    op.create_index('idx_arxiv_processing_nas_ip', 'arxiv_processing_status', ['nas_ip'])
    op.create_index('idx_arxiv_processing_status', 'arxiv_processing_status', ['processing_status'])
    op.create_index('idx_arxiv_processing_processed', 'arxiv_processing_status', ['is_processed'])

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###

    # 删除索引
    op.drop_index('idx_arxiv_processing_processed', table_name='arxiv_processing_status')
    op.drop_index('idx_arxiv_processing_status', table_name='arxiv_processing_status')
    op.drop_index('idx_arxiv_processing_nas_ip', table_name='arxiv_processing_status')
    op.drop_index('idx_arxiv_processing_file_hash', table_name='arxiv_processing_status')
    op.drop_index('idx_arxiv_processing_file_path', table_name='arxiv_processing_status')

    # 删除表
    op.drop_table('arxiv_processing_status')

    # ### end Alembic commands ###