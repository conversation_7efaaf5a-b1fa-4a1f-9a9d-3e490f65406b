"""
Ollama本地模型提供商
"""

from typing import List
import structlog

from ..base import BaseModelProvider
from ..models import (
    ModelConfig, ModelProviderConfig, ModelCapability, 
    ModelStatus, ModelProviderType
)

logger = structlog.get_logger(__name__)


class OllamaProvider(BaseModelProvider):
    """Ollama本地模型提供商"""
    
    async def _initialize(self) -> None:
        """初始化Ollama提供商"""
        # 设置默认API基础URL
        if not self.config.api_base:
            self.config.api_base = "http://localhost:11434"
        
        logger.info(f"Ollama提供商初始化完成，API基础URL: {self.config.api_base}")
    
    async def _get_models(self) -> List[ModelConfig]:
        """获取Ollama模型列表"""
        # 只从配置中获取模型，不使用硬编码
        if not self.config.models:
            logger.warning("Ollama提供商配置中没有定义模型")
            return []

        return self.config.models.copy()
    
    async def _validate_model(self, model: ModelConfig) -> bool:
        """验证Ollama模型"""
        # 这里可以实现具体的模型验证逻辑
        # 例如调用Ollama API检查模型是否已安装
        return model.status == ModelStatus.AVAILABLE
