"""
README生成主处理器
"""

import json
from typing import Dict, Any, Optional
from tornado.web import HTTPError
import structlog
from dependency_injector.wiring import inject, Provide

from app.api.base import BaseHandler
from app.core.di.containers import Container
from app.services.readme_generation.generation_manager import GenerationManager

logger = structlog.get_logger(__name__)


class ReadmeGenerationHandler(BaseHandler):
    """README生成主处理器"""

    @inject
    def initialize(
        self,
        readme_generation_manager: GenerationManager = Provide[Container.readme_generation_manager],
    ):
        super().initialize()
        self.readme_generation_manager = readme_generation_manager

    async def post(self):
        """生成完整的README文档"""
        try:
            if not self.json_body:
                raise HTTPError(400, "请求体不能为空")

            project_path = self.json_body.get("project_path")
            project_name = self.json_body.get("project_name")
            project_url = self.json_body.get("project_url")
            model_name = self.json_body.get("model_name", "default")
            provider_name = self.json_body.get("provider_name")
            custom_prompt = self.json_body.get("custom_prompt")
            format_type = self.json_body.get("format_type", "markdown")
            created_by = self.json_body.get("created_by", "api_user")

            if not project_path:
                raise HTTPError(400, "project_path参数是必需的")

            # 步骤1：生成逻辑树（内部已集成项目分析）
            logger.info("开始生成逻辑树", project_path=project_path)
            tree_result = await self.readme_generation_manager.generate_logic_tree(
                project_path=project_path,
                project_name=project_name,
                project_url=project_url,
                model_name=model_name,
                provider_name=provider_name,
                custom_prompt=custom_prompt
            )

            if not tree_result.get("success"):
                error_msg = tree_result.get("error", "逻辑树生成失败")
                raise HTTPError(500, error_msg)

            tree_id = tree_result.get("tree_id")
            project_id = tree_result.get("project_context",{}).get("project_id")

            if not tree_id:
                raise HTTPError(500, "逻辑树ID获取失败")

            # 步骤2：生成所有节点内容
            logger.info("开始生成节点内容", tree_id=tree_id)
            content_result = await self.readme_generation_manager.generate_all_content(
                tree_id=tree_id,
                model_name=model_name,
                provider_name=provider_name,
                created_by=created_by
            )

            if not content_result.get("success"):
                error_msg = content_result.get("error", "节点内容生成失败")
                raise HTTPError(500, error_msg)

            # 步骤3：导出README文档
            logger.info("开始导出README", tree_id=tree_id, format_type=format_type)
            readme_content = await self.readme_generation_manager.export_readme(
                tree_id=tree_id,
                format_type=format_type
            )

            if not readme_content:
                raise HTTPError(500, "README导出失败")

            # 返回完整结果
            response_data = {
                "tree_id": tree_id,
                "project_id": project_id,
                "readme_content": readme_content,
                "format_type": format_type,
                "generated_nodes": len(content_result.get("generated_results", {}).get("results", {})),
                "total_nodes": content_result.get("generated_results",{}).get("total_steps", 0),
                "generation_summary": {
                    "tree_generation": tree_result.get("message", "逻辑树生成成功"),
                    "content_generation": content_result.get("message", "内容生成成功"),
                    "export_format": format_type
                }
            }

            self.success_response(response_data, "README生成成功")

        except HTTPError:
            raise
        except Exception as e:
            logger.error("README生成失败", error=str(e), exc_info=e)
            raise HTTPError(500, f"README生成失败: {str(e)}")
