#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/9/15 13:44
# @File    : nas_arxiv_utils.py
# @Description: NAS Arxiv数据处理工具类，用于处理从NAS下载的Arxiv论文数据
"""


import os
import tarfile
import gzip
import logging
import tempfile
import shutil
from typing import List, Dict, Optional, Generator, Tuple
from pathlib import Path
import json
import re
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class ArxivPaper:
    """Arxiv论文数据结构"""
    paper_id: str
    title: str
    authors: List[str]
    abstract: str
    content: str
    categories: List[str]
    created_date: Optional[datetime] = None
    updated_date: Optional[datetime] = None
    file_path: Optional[str] = None


@dataclass
class ArxivChunk:
    """Arxiv论文分段数据结构"""
    chunk_id: str
    paper_id: str
    content: str
    chunk_type: str  # 'title', 'abstract', 'content'
    chunk_index: int
    metadata: Dict


class NASArxivUtil:
    """NAS Arxiv数据处理工具类"""

    def __init__(self, temp_dir: str = None):
        """
        初始化Arxiv处理工具

        Args:
            temp_dir: 临时目录，如果为None则使用系统临时目录
        """
        self.temp_dir = temp_dir or tempfile.gettempdir()
        self.extracted_dir = None

    def extract_arxiv_archive(self, archive_path: str) -> Optional[str]:
        """
        解压Arxiv压缩文件（支持tar格式）
        
        根据你的描述：第一层tar解压完是一个文件夹，再解压是第二层一堆gz

        Args:
            archive_path: 压缩文件路径

        Returns:
            str: 解压后的目录路径，失败返回None
        """
        try:
            # 创建解压目录
            extract_dir = os.path.join(self.temp_dir, f"arxiv_extract_{os.getpid()}")
            os.makedirs(extract_dir, exist_ok=True)

            logger.info(f"开始解压文件: {archive_path}")

            # 第一层解压：tar文件
            with tarfile.open(archive_path, 'r') as tar:
                tar.extractall(extract_dir)

            # 第二层解压：处理解压出来的gz文件
            self._extract_gz_files(extract_dir)

            self.extracted_dir = extract_dir
            logger.info(f"解压完成，文件保存在: {extract_dir}")
            return extract_dir

        except Exception as e:
            logger.error(f"解压文件时发生错误: {e}")
            return None

    def _extract_gz_files(self, extract_dir: str):
        """
        解压目录中的所有gz文件
        
        Args:
            extract_dir: 解压目录
        """
        for root, dirs, files in os.walk(extract_dir):
            for file in files:
                if file.endswith('.gz'):
                    gz_path = os.path.join(root, file)
                    try:
                        # 解压gz文件
                        with gzip.open(gz_path, 'rb') as gz_file:
                            # 创建目标文件路径（去掉.gz扩展名）
                            target_path = gz_path[:-3]
                            with open(target_path, 'wb') as target_file:
                                shutil.copyfileobj(gz_file, target_file)
                        
                        # 删除原gz文件
                        os.remove(gz_path)
                        logger.debug(f"解压gz文件: {file}")
                        
                    except Exception as e:
                        logger.warning(f"解压gz文件 {file} 失败: {e}")

    def get_paper_files(self, extract_dir: str) -> List[str]:
        """
        获取解压后的论文文件列表（只获取tex文件）

        Args:
            extract_dir: 解压目录

        Returns:
            list: 论文文件路径列表
        """
        paper_files = []

        try:
            for root, dirs, files in os.walk(extract_dir):
                for file in files:
                    # 只处理tex文件
                    if file.endswith('.tex'):
                        paper_files.append(os.path.join(root, file))

            logger.info(f"找到 {len(paper_files)} 个tex论文文件")
            return paper_files

        except Exception as e:
            logger.error(f"获取论文文件列表时发生错误: {e}")
            return []

    def parse_tex_file(self, file_path: str) -> Optional[ArxivPaper]:
        """
        解析LaTeX文件，提取论文信息

        Args:
            file_path: LaTeX文件路径

        Returns:
            ArxivPaper: 论文信息对象，失败返回None
        """
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # 提取论文ID（从文件名或内容中）
            paper_id = self._extract_paper_id(file_path, content)

            # 提取标题
            title = self._extract_title(content)

            # 提取作者
            authors = self._extract_authors(content)

            # 提取摘要
            abstract = self._extract_abstract(content)

            # 提取分类
            categories = self._extract_categories(content)

            # 提取日期
            created_date, updated_date = self._extract_dates(content)

            # 清理内容中的LaTeX命令
            cleaned_content = self._clean_latex_commands(content)

            return ArxivPaper(
                paper_id=paper_id,
                title=title,
                authors=authors,
                abstract=abstract,
                content=cleaned_content,
                categories=categories,
                created_date=created_date,
                updated_date=updated_date,
                file_path=file_path
            )

        except Exception as e:
            logger.error(f"解析LaTeX文件时发生错误: {e}")
            return None

    def _extract_paper_id(self, file_path: str, content: str) -> str:
        """提取论文ID"""
        # 从文件名提取
        filename = os.path.basename(file_path)
        if filename.startswith('arxiv_'):
            return filename.replace('arxiv_', '').replace('.tex', '')

        # 从内容中提取
        arxiv_pattern = r'arXiv:(\d{4}\.\d{4,5}(?:v\d+)?)'
        match = re.search(arxiv_pattern, content)
        if match:
            return match.group(1)

        return os.path.splitext(filename)[0]

    def _extract_title(self, content: str) -> str:
        """提取标题"""
        title_patterns = [
            r'\\title\{([^}]+)\}',
            r'\\title\s*\{([^}]+)\}',
            r'\\title\s*\[([^\]]+)\]\s*\{([^}]+)\}'
        ]

        for pattern in title_patterns:
            match = re.search(pattern, content, re.IGNORECASE | re.DOTALL)
            if match:
                title = match.group(1) if len(match.groups()) == 1 else match.group(2)
                return self._clean_latex_commands(title)

        return "未知标题"

    def _extract_authors(self, content: str) -> List[str]:
        """提取作者列表"""
        author_patterns = [
            r'\\author\{([^}]+)\}',
            r'\\author\s*\{([^}]+)\}'
        ]

        for pattern in author_patterns:
            match = re.search(pattern, content, re.IGNORECASE | re.DOTALL)
            if match:
                authors_text = match.group(1)
                # 简单的作者分割逻辑
                authors = [author.strip() for author in re.split(r'[,\n]', authors_text)]
                return [self._clean_latex_commands(author) for author in authors if author]

        return []

    def _extract_abstract(self, content: str) -> str:
        """提取摘要"""
        abstract_patterns = [
            r'\\begin\{abstract\}(.*?)\\end\{abstract\}',
            r'\\abstract\{([^}]+)\}'
        ]

        for pattern in abstract_patterns:
            match = re.search(pattern, content, re.IGNORECASE | re.DOTALL)
            if match:
                abstract = match.group(1)
                return self._clean_latex_commands(abstract)

        return ""

    def _extract_categories(self, content: str) -> List[str]:
        """提取分类"""
        category_patterns = [
            r'\\category\{([^}]+)\}',
            r'\\categories\{([^}]+)\}',
            r'\\subject\{([^}]+)\}'
        ]

        categories = []
        for pattern in category_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                cats = [cat.strip() for cat in match.split(',')]
                categories.extend(cats)

        return list(set(categories))

    def _extract_dates(self, content: str) -> Tuple[Optional[datetime], Optional[datetime]]:
        """提取日期"""
        date_patterns = [
            r'\\date\{([^}]+)\}',
            r'\\created\{([^}]+)\}',
            r'\\updated\{([^}]+)\}'
        ]

        created_date = None
        updated_date = None

        for pattern in date_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                date_str = match.group(1)
                try:
                    # 尝试多种日期格式
                    date_formats = ['%Y-%m-%d', '%Y/%m/%d', '%d %B %Y', '%B %d, %Y']
                    for fmt in date_formats:
                        try:
                            date_obj = datetime.strptime(date_str, fmt)
                            if 'created' in pattern:
                                created_date = date_obj
                            elif 'updated' in pattern:
                                updated_date = date_obj
                            else:
                                created_date = date_obj
                            break
                        except ValueError:
                            continue
                except:
                    pass

        return created_date, updated_date

    def _clean_latex_commands(self, text: str) -> str:
        """清理LaTeX命令"""
        # 移除常见的LaTeX命令
        text = re.sub(r'\\[a-zA-Z]+\{[^}]*\}', '', text)
        text = re.sub(r'\\[a-zA-Z]+', '', text)
        text = re.sub(r'\{[^}]*\}', '', text)
        text = re.sub(r'\\[^a-zA-Z]', '', text)
        text = re.sub(r'\s+', ' ', text)
        return text.strip()

    def get_data(self, archive_path: str, max_papers: int = 100, 
                 chunk_size: int = 1000, chunk_overlap: int = 200) -> List[ArxivChunk]:
        """
        主要接口：处理tar.gz文件，返回分段后的论文数据
        
        Args:
            archive_path: 压缩文件路径
            max_papers: 最大处理论文数量
            chunk_size: 分段大小
            chunk_overlap: 分段重叠大小
            
        Returns:
            List[ArxivChunk]: 分段后的论文数据列表
        """
        chunks = []
        
        try:
            # 解压文件
            extract_dir = self.extract_arxiv_archive(archive_path)
            if not extract_dir:
                return chunks

            # 获取论文文件
            paper_files = self.get_paper_files(extract_dir)

            # 限制处理数量
            files_to_process = paper_files[:max_papers]

            logger.info(f"开始处理 {len(files_to_process)} 个论文文件")

            for file_path in files_to_process:
                paper = self.parse_tex_file(file_path)
                if paper:
                    # 对论文进行分段
                    paper_chunks = self._chunk_paper(paper, chunk_size, chunk_overlap)
                    chunks.extend(paper_chunks)
                    logger.info(f"处理完成: {paper.paper_id} - 生成 {len(paper_chunks)} 个分段")

            logger.info(f"处理完成，共生成 {len(chunks)} 个分段")
            return chunks

        except Exception as e:
            logger.error(f"处理Arxiv文件时发生错误: {e}")
            return chunks

    def _chunk_paper(self, paper: ArxivPaper, chunk_size: int, chunk_overlap: int) -> List[ArxivChunk]:
        """
        对单篇论文进行分段
        
        Args:
            paper: 论文对象
            chunk_size: 分段大小
            chunk_overlap: 分段重叠大小
            
        Returns:
            List[ArxivChunk]: 分段列表
        """
        chunks = []
        
        # 基础元数据
        base_metadata = {
            'title': paper.title,
            'authors': paper.authors,
            'categories': paper.categories,
            'abstract': paper.abstract[:200] + "..." if len(paper.abstract) > 200 else paper.abstract,
            'created_date': paper.created_date.isoformat() if paper.created_date else None,
            'updated_date': paper.updated_date.isoformat() if paper.updated_date else None,
            'file_path': paper.file_path
        }
        
        chunk_index = 0
        
        # 1. 标题作为一个分段
        if paper.title:
            chunks.append(ArxivChunk(
                chunk_id=f"{paper.paper_id}_title",
                paper_id=paper.paper_id,
                content=paper.title,
                chunk_type='title',
                chunk_index=chunk_index,
                metadata={**base_metadata, 'chunk_type': 'title'}
            ))
            chunk_index += 1
        
        # 2. 摘要作为一个分段
        if paper.abstract:
            chunks.append(ArxivChunk(
                chunk_id=f"{paper.paper_id}_abstract",
                paper_id=paper.paper_id,
                content=paper.abstract,
                chunk_type='abstract',
                chunk_index=chunk_index,
                metadata={**base_metadata, 'chunk_type': 'abstract'}
            ))
            chunk_index += 1
        
        # 3. 正文内容分段
        if paper.content:
            content_chunks = self._split_text(paper.content, chunk_size, chunk_overlap)
            
            for i, chunk_content in enumerate(content_chunks):
                chunks.append(ArxivChunk(
                    chunk_id=f"{paper.paper_id}_content_{i}",
                    paper_id=paper.paper_id,
                    content=chunk_content,
                    chunk_type='content',
                    chunk_index=chunk_index,
                    metadata={
                        **base_metadata, 
                        'chunk_type': 'content',
                        'content_chunk_index': i
                    }
                ))
                chunk_index += 1
        
        return chunks

    def _split_text(self, text: str, chunk_size: int, chunk_overlap: int) -> List[str]:
        """
        分割文本为指定大小的块
        
        Args:
            text: 待分割文本
            chunk_size: 块大小
            chunk_overlap: 重叠大小
            
        Returns:
            List[str]: 文本块列表
        """
        if len(text) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # 如果不是最后一块，尝试在句子边界处分割
            if end < len(text):
                # 寻找句号、问号、感叹号等句子结束符
                sentence_end = max(
                    text.rfind('.', start, end),
                    text.rfind('!', start, end),
                    text.rfind('?', start, end),
                    text.rfind('\n\n', start, end),  # 段落分隔
                    text.rfind('\n', start, end)
                )
                
                if sentence_end > start + chunk_size // 2:
                    end = sentence_end + 1
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            # 计算下一个开始位置（考虑重叠）
            start = end - chunk_overlap
            
            # 确保不会无限循环
            if start <= 0:
                start = end
        
        return chunks

    def process_arxiv_archive(self, archive_path: str, max_papers: int = 100) -> List[ArxivPaper]:
        """
        处理Arxiv压缩文件，提取论文信息（保持向后兼容）

        Args:
            archive_path: 压缩文件路径
            max_papers: 最大处理论文数量

        Returns:
            list: 论文信息列表
        """
        papers = []

        try:
            # 解压文件
            extract_dir = self.extract_arxiv_archive(archive_path)
            if not extract_dir:
                return papers

            # 获取论文文件
            paper_files = self.get_paper_files(extract_dir)

            # 限制处理数量
            files_to_process = paper_files[:max_papers]

            logger.info(f"开始处理 {len(files_to_process)} 个论文文件")

            for file_path in files_to_process:
                paper = self.parse_tex_file(file_path)
                if paper:
                    papers.append(paper)
                    logger.info(f"处理完成: {paper.paper_id} - {paper.title[:50]}...")

            logger.info(f"处理完成，共提取 {len(papers)} 篇论文")
            return papers

        except Exception as e:
            logger.error(f"处理Arxiv文件时发生错误: {e}")
            return papers

    def create_paper_index(self, papers: List[ArxivPaper]) -> Dict[str, Dict]:
        """
        创建论文索引，用于快速查找

        Args:
            papers: 论文列表

        Returns:
            dict: 索引字典
        """
        index = {}

        for paper in papers:
            # 按论文ID索引
            index[paper.paper_id] = {
                'title': paper.title,
                'authors': paper.authors,
                'abstract': paper.abstract,
                'categories': paper.categories,
                'created_date': paper.created_date.isoformat() if paper.created_date else None,
                'updated_date': paper.updated_date.isoformat() if paper.updated_date else None,
                'file_path': paper.file_path
            }

            # 按标题关键词索引
            title_words = re.findall(r'\b\w+\b', paper.title.lower())
            for word in title_words:
                if len(word) > 2:  # 忽略太短的词
                    if word not in index:
                        index[word] = []
                    index[word].append(paper.paper_id)

            # 按作者索引
            for author in paper.authors:
                author_key = f"author:{author.lower()}"
                if author_key not in index:
                    index[author_key] = []
                index[author_key].append(paper.paper_id)

        return index

    def search_papers(self, index: Dict, query: str, search_type: str = "title") -> List[str]:
        """
        搜索论文

        Args:
            index: 索引字典
            query: 搜索查询
            search_type: 搜索类型 ("title", "author", "abstract")

        Returns:
            list: 匹配的论文ID列表
        """
        query = query.lower()
        results = set()

        if search_type == "title":
            # 标题搜索
            for word in query.split():
                if word in index:
                    results.update(index[word])

        elif search_type == "author":
            # 作者搜索
            author_key = f"author:{query}"
            if author_key in index:
                results.update(index[author_key])

        elif search_type == "abstract":
            # 摘要搜索（简单实现）
            for paper_id, paper_info in index.items():
                if isinstance(paper_info, dict) and 'abstract' in paper_info:
                    if query in paper_info['abstract'].lower():
                        results.add(paper_id)

        return list(results)

    def cleanup(self):
        """清理临时文件"""
        if self.extracted_dir and os.path.exists(self.extracted_dir):
            try:
                shutil.rmtree(self.extracted_dir)
                logger.info("已清理临时文件")
            except Exception as e:
                logger.error(f"清理临时文件时发生错误: {e}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.cleanup()