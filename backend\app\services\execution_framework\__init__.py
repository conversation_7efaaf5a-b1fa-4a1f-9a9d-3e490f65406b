"""
执行框架模块

AI驱动的执行引擎，整合描述器、工具调用、流程编排等功能
"""

# 基础组件
from .base.descriptor import BaseDescriptor
from .base.step_type import StepType

# 模型定义
from .models.context import ExecutionContext, ExecutionContextBuilder
from .models.descriptor_config import DescriptorConfig
from .models.tool_instruction import ToolInstruction
from .models.execution_config import ExecutionConfig, ApplicabilityRule, ExecutionConfigBuilder

# 工具管理
from .tools.tool_manager import ToolManager
from .tools.base_handler import BaseToolHandler
from .tools.handlers import (
    CodebaseRetrievalHandler,
    ViewFileHandler,
    ViewDirectoryHandler,
    SaveFileHandler,
    RemoveFilesHandler,
    StrReplaceEditorHandler,
    WebSearchHandler,
    WebFetchHandler,
    ProcessHandler
)

# 执行器组件
from .executor.descriptor_executor import DescriptorExecutor
from .executor.workflow_orchestrator import WorkflowOrchestrator, ExecutionStep, ExecutionStatus
from .executor.tool_wrapper import (
    DescriptorToolWrapper,
    ExecutionFrameworkToolWrapper
)
from .executor.monitoring import (
    ExecutionMonitor,
    MetricsCollector,
    EventLogger,
    PerformanceMonitor,
    ExecutionMetric,
    ExecutionEvent,
    MetricType
)

# 主执行引擎
from .execution_engine import ExecutionEngine

__all__ = [
    # 基础组件
    "BaseDescriptor",
    "StepType",
    
    # 模型定义
    "ExecutionContext",
    "ExecutionContextBuilder",
    "DescriptorConfig",
    "ToolInstruction",
    "ExecutionConfig",
    "ApplicabilityRule",
    "ExecutionConfigBuilder",
    
    # 工具管理
    "ToolManager",
    "BaseToolHandler",
    "CodebaseRetrievalHandler",
    "ViewFileHandler",
    "ViewDirectoryHandler",
    "SaveFileHandler",
    "RemoveFilesHandler",
    "StrReplaceEditorHandler",
    "WebSearchHandler",
    "WebFetchHandler",
    "ProcessHandler",
    
    # 执行器组件
    "DescriptorExecutor",
    "WorkflowOrchestrator",
    "ExecutionStep",
    "ExecutionStatus",
    "DescriptorToolWrapper",
    "ExecutionFrameworkToolWrapper",
    "ExecutionMonitor",
    "MetricsCollector",
    "EventLogger",
    "PerformanceMonitor",
    "ExecutionMetric",
    "ExecutionEvent",
    "MetricType",
    
    # 主执行引擎
    "ExecutionEngine"
]

# 版本信息
__version__ = "1.0.0"
__author__ = "Execution Framework Team"
__description__ = "AI驱动的执行引擎，基于LangChain实现"

# 模块级别的便捷函数
def create_basic_engine():
    """
    创建基础执行引擎实例
    
    Returns:
        ExecutionEngine: 执行引擎实例
    """
    return ExecutionEngine()

def create_basic_context(
    process_id: str,
    process_name: str,
    process_type: str = "default",
    workspace_path: str = None
) -> ExecutionContext:
    """
    创建基础执行上下文
    
    Args:
        process_id: 进程ID
        process_name: 进程名称
        process_type: 进程类型
        workspace_path: 工作空间路径
        
    Returns:
        ExecutionContext: 执行上下文实例
    """
    return ExecutionContextBuilder.create_basic_context(
        process_id=process_id,
        process_name=process_name,
        process_type=process_type,
        workspace_path=workspace_path
    )

def create_tool_wrapper(tool_manager: ToolManager = None) -> ExecutionFrameworkToolWrapper:
    """
    创建工具封装器
    
    Args:
        tool_manager: 工具管理器，如果为None则创建新实例
        
    Returns:
        ExecutionFrameworkToolWrapper: 工具封装器实例
    """
    return ExecutionFrameworkToolWrapper(tool_manager)
