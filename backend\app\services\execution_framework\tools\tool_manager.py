"""
工具调用管理器

基于LangChain实现的工具调用统一管理器，支持各种工具类型的动态调用和结果处理
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Union, Callable, Awaitable, Type
from pydantic import BaseModel

from langchain.tools import BaseTool
from langchain.schema import BaseMessage
from langchain_core.tools import ToolException

from ..models.tool_instruction import ToolInstruction
from ..models.context import ExecutionContext
from .base_handler import BaseToolHandler
from ...codebase.manager import CodeBaseManager
from .handlers import (
    CodebaseRetrievalHandler,
    ViewFileHandler,
    ViewDirectoryHandler,
    SaveFileHandler,
    RemoveFilesHandler,
    StrReplaceEditorHandler,
    WebSearchHandler,
    WebFetchHandler,
    ProcessHandler
)

logger = logging.getLogger(__name__)


class ToolManager:
    """工具调用管理器"""

    def __init__(self, codebase_manager: Optional[CodeBaseManager] = None):
        self.handlers: Dict[str, BaseToolHandler] = {}
        self.codebase_manager = codebase_manager
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """注册默认工具处理器"""
        self.handlers["codebase-retrieval"] = CodebaseRetrievalHandler(self.codebase_manager)
        self.handlers["view-file"] = ViewFileHandler()
        self.handlers["view-directory"] = ViewDirectoryHandler()
        self.handlers["save-file"] = SaveFileHandler()
        self.handlers["remove-files"] = RemoveFilesHandler()
        self.handlers["str-replace-editor"] = StrReplaceEditorHandler()
        self.handlers["web-search"] = WebSearchHandler()
        self.handlers["web-fetch"] = WebFetchHandler()
        self.handlers["launch-process"] = ProcessHandler()

    def register_handler(self, tool_name: str, handler: BaseToolHandler):
        """
        注册工具处理器

        Args:
            tool_name: 工具名称
            handler: 工具处理器
        """
        self.handlers[tool_name] = handler
        logger.info(f"注册工具处理器: {tool_name}")

    def get_tool_args_schema(self, tool_name: str) -> Optional[Type[BaseModel]]:
        """
        获取工具的参数模型定义

        Args:
            tool_name: 工具名称

        Returns:
            Optional[Type[BaseModel]]: 参数模型类，如果工具不存在则返回None
        """
        handler = self.handlers.get(tool_name)
        if handler:
            return handler.get_args_schema()
        return None
    
    async def execute_instruction(
        self, 
        instruction: ToolInstruction, 
        context: ExecutionContext
    ) -> Dict[str, Any]:
        """
        执行工具指令
        
        Args:
            instruction: 工具指令
            context: 执行上下文
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        try:
            # 检查是否应该执行
            if not instruction.should_execute(context.model_dump()):
                logger.info(f"跳过工具指令: {instruction.description}")
                return {
                    "skipped": True,
                    "reason": "条件不满足或非必需",
                    "instruction": instruction.description
                }

            # 获取处理器
            handler = self.handlers.get(instruction.name)
            if not handler:
                raise ToolException(f"未找到工具处理器: {instruction.name}")

            # 处理参数
            parameters = instruction.get_parameters_with_context(context.model_dump())
            
            # 验证参数
            validation_errors = handler.validate_parameters(parameters)
            if validation_errors:
                raise ToolException(f"参数验证失败: {', '.join(validation_errors)}")
            
            # 执行工具
            logger.info(f"执行工具指令: {instruction.description}")
            result = await handler.execute(parameters, context)
            
            # 存储结果到上下文
            context.set_variable(instruction.result_key, result)
            
            return {
                "success": True,
                "result": result,
                "instruction": instruction.description,
                "result_key": instruction.result_key
            }
            
        except Exception as e:
            logger.error(f"工具指令执行失败: {instruction.description} - {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "instruction": instruction.description,
                "result_key": instruction.result_key
            }
    
    async def execute_instructions(
        self, 
        instructions: List[ToolInstruction], 
        context: ExecutionContext
    ) -> Dict[str, Any]:
        """
        批量执行工具指令
        
        Args:
            instructions: 工具指令列表
            context: 执行上下文
            
        Returns:
            Dict[str, Any]: 批量执行结果
        """
        # 按顺序排序
        sorted_instructions = sorted(instructions, key=lambda x: x.order)
        
        results = {}
        errors = []
        
        for instruction in sorted_instructions:
            try:
                result = await self.execute_instruction(instruction, context)
                results[instruction.result_key] = result
                
                if not result.get("success", True):
                    errors.append(f"{instruction.description}: {result.get('error', '未知错误')}")
                    
            except Exception as e:
                error_msg = f"{instruction.description}: {str(e)}"
                errors.append(error_msg)
                results[instruction.result_key] = {
                    "success": False,
                    "error": str(e),
                    "instruction": instruction.description
                }
        
        return {
            "total": len(instructions),
            "completed": len(results),
            "errors": errors,
            "results": results,
            "success": len(errors) == 0
        }
