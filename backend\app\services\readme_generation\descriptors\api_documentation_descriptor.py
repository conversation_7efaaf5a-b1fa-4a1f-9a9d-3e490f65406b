"""
API文档生成描述器

通过提示词指导LLM生成项目API接口文档
"""

import logging
from typing import List

from ...execution_framework.base.step_type import StepType
from ...execution_framework.models.context import ExecutionContext
from ...execution_framework.models.tool_instruction import ToolInstruction
from .base_generation_descriptor import BaseGenerationDescriptor

logger = logging.getLogger(__name__)


class ApiDocumentationDescriptor(BaseGenerationDescriptor):
    """API文档生成描述器 - 指导LLM生成API接口文档"""

    def __init__(self):
        """初始化API文档生成描述器"""
        super().__init__(StepType.API_DOCUMENTATION, "api_documentation")

    def get_description(self) -> str:
        """获取描述器描述"""
        return "指导LLM生成项目API接口文档和使用说明"

    def get_tool_instructions(self, context: ExecutionContext) -> List[ToolInstruction]:
        """获取工具调用指令列表 - 每个工具只定义一次"""
        return [
            ToolInstruction(
                name="codebase-retrieval",
                parameters={
                    "information_request": "API接口定义、路由配置、请求响应格式、认证方式、错误处理"
                },
                description="获取项目API相关信息",
                result_key="codebase_analysis"
            ),
            ToolInstruction(
                name="view-file",
                parameters={"path": "openapi.yaml"},
                description="查看API规范文档（可多次调用查看不同文件）",
                result_key="api_specs"
            ),
            ToolInstruction(
                name="view-directory",
                parameters={"path": "api"},
                description="查看API目录结构",
                result_key="api_directory"
            )
        ]

    def get_prompt_template(self) -> str:
        """获取提示词模板 - 指导LLM如何生成API文档"""
        return """
## 项目信息
项目名称: {project_name}
项目路径: {project_path}
项目类型: {project_type}
编程语言: {programming_languages}
技术框架: {frameworks}

## 代码库分析
{codebase_analysis}

## API规范文档
{api_specs}

## API目录结构
{api_directory}

## 自定义要求
{custom_prompt}

## 生成任务

请基于以上项目信息，使用可用工具深入分析后，生成详细的API文档。

### 工具使用指导：
1. 使用 codebase-retrieval 获取API接口定义和实现
2. 使用 view-file 查看多个API规范文件：
   - openapi.yaml（OpenAPI 3.0规范）
   - swagger.json（Swagger文档）
   - api.md（API文档）
   - postman.json（Postman集合）
3. 使用 view-directory 查看API相关目录结构

### 重点包含：
1. API概览和基础信息（基础URL、版本、认证等）
2. 主要接口列表和分类
3. 详细的接口文档（请求方法、参数、响应格式）
4. 认证和授权说明
5. 错误码和异常处理
6. 使用示例和最佳实践
"""

    def get_input_variables(self) -> List[str]:
        """获取输入变量列表"""
        return [
            "project_name",
            "project_path",
            "project_type",
            "programming_languages",
            "frameworks",
            "codebase_analysis",
            "api_specs",
            "api_directory",
            "custom_prompt"
        ]

    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return "你是一个专业的API文档工程师，擅长编写清晰完整的接口文档和使用指南。"

    def get_constraints(self) -> List[str]:
        """将写作要求迁移为约束条件"""
        return [
            "使用Markdown格式",
            "提供完整的请求响应示例",
            "内容必须基于实际项目API代码",
            "输出包含：API概览、接口列表、详细文档、认证说明、错误处理、使用示例",
            "每个接口提供curl命令示例",
            "包含完整的参数说明和数据类型",
            "总长度控制在1200-2000字",
        ]

    def get_priority(self, context: ExecutionContext) -> int:
        """获取优先级 - API文档优先级中等"""
        return 75

    def get_dependencies(self) -> List[str]:
        """获取依赖 - 依赖使用说明"""
        return ["usage"]
