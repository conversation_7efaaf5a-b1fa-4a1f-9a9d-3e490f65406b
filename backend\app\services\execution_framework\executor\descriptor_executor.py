"""
描述符执行器

基于LangChain AgentExecutor模式的描述符执行器，整合工具封装和模型提供商管理
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

from langchain.agents import AgentExecutor, create_tool_calling_agent
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate, PromptTemplate
from langchain_core.tools import BaseTool
from langchain_core.callbacks import BaseCallbackHandler
from langchain_core.outputs import LLMResult

from ..base.descriptor import BaseDescriptor
from ..models.context import ExecutionContext
from ..models.descriptor_config import DescriptorConfig
from .tool_wrapper import ExecutionFrameworkToolWrapper
from ..tools.tool_manager import ToolManager
from ...model_providers.manager import ModelProviderManager
from ...model_providers.models import ModelConfig
from ...codebase.manager import CodeBaseManager
from ....utils.retry import retry_async

logger = logging.getLogger(__name__)


class DebugStreamingCallback(BaseCallbackHandler):
    """简单的流式输出回调，用于调试模型信息"""

    def __init__(self, enable_debug: bool = True):
        """
        初始化调试回调

        Args:
            enable_debug: 是否启用调试输出
        """
        self.enable_debug = enable_debug
        self.step_count = 0

    def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs) -> None:
        """LLM开始时的回调"""
        if self.enable_debug:
            model_name = serialized.get("id", ["unknown"])[-1] if serialized.get("id") else "unknown"
            logger.info(f"🤖 LLM开始执行 - 模型: {model_name}")
            logger.info(f"📝 提示内容: {prompts[0][:200]}..." if prompts and len(prompts[0]) > 200 else f"📝 提示内容: {prompts[0] if prompts else 'None'}")

    def on_llm_end(self, response: LLMResult, **kwargs) -> None:
        """LLM结束时的回调"""
        if self.enable_debug:
            if response.generations:
                output = response.generations[0][0].text if response.generations[0] else "无输出"
                logger.info(f"✅ LLM执行完成 - 输出长度: {len(output)} 字符")
                logger.info(f"📤 LLM输出: {output[:300]}..." if len(output) > 300 else f"📤 LLM输出: {output}")

            # 输出token使用信息
            if hasattr(response, 'llm_output') and response.llm_output:
                token_usage = response.llm_output.get('token_usage', {})
                if token_usage:
                    logger.info(f"🔢 Token使用 - 输入: {token_usage.get('prompt_tokens', 0)}, "
                              f"输出: {token_usage.get('completion_tokens', 0)}, "
                              f"总计: {token_usage.get('total_tokens', 0)}")

    def on_llm_error(self, error: Exception, **kwargs) -> None:
        """LLM出错时的回调"""
        if self.enable_debug:
            logger.error(f"❌ LLM执行出错: {str(error)}")

    def on_tool_start(self, serialized: Dict[str, Any], input_str: str, **kwargs) -> None:
        """工具开始执行时的回调"""
        if self.enable_debug:
            tool_name = serialized.get("name", "unknown")
            self.step_count += 1
            logger.info(f"🔧 步骤 {self.step_count} - 工具开始: {tool_name}")
            logger.info(f"📥 工具输入: {input_str[:200]}..." if len(input_str) > 200 else f"📥 工具输入: {input_str}")

    def on_tool_end(self, output: str, **kwargs) -> None:
        """工具结束执行时的回调"""
        if self.enable_debug:
            logger.info(f"✅ 工具执行完成 - 输出长度: {len(output)} 字符")
            logger.info(f"📤 工具输出: {output[:200]}..." if len(output) > 200 else f"📤 工具输出: {output}")

    def on_tool_error(self, error: Exception, **kwargs) -> None:
        """工具出错时的回调"""
        if self.enable_debug:
            logger.error(f"❌ 工具执行出错: {str(error)}")

    def on_agent_action(self, action, **kwargs) -> None:
        """Agent执行动作时的回调"""
        if self.enable_debug:
            logger.info(f"🎯 Agent动作: {action.tool} - {action.tool_input}")

    def on_agent_finish(self, finish, **kwargs) -> None:
        """Agent完成时的回调"""
        if self.enable_debug:
            logger.info(f"🏁 Agent执行完成")
            logger.info(f"📋 最终输出: {finish.return_values}")


class DescriptorExecutor:
    """描述符执行器
    
    基于LangChain AgentExecutor模式执行描述符，自动将工具指令转换为Agent可用的工具
    """
    
    def __init__(
        self,
        model_provider_manager: Optional[ModelProviderManager] = None,
        tool_manager: Optional[ToolManager] = None,
        codebase_manager: Optional[CodeBaseManager] = None,
        enable_debug_callback: bool = True
    ):
        """
        初始化描述符执行器

        Args:
            model_provider_manager: 模型提供商管理器，如果为None则创建新实例
            tool_manager: 工具管理器，如果为None则创建新实例
            codebase_manager: 代码库管理器，用于代码检索功能
            enable_debug_callback: 是否启用调试回调
        """
        self.model_provider_manager = model_provider_manager or ModelProviderManager()
        self.codebase_manager = codebase_manager
        self.tool_manager = tool_manager or ToolManager(codebase_manager)
        self.tool_wrapper = ExecutionFrameworkToolWrapper(self.tool_manager)
        self.enable_debug_callback = enable_debug_callback
        self._initialized = False
    
    async def initialize(self) -> bool:
        """
        初始化执行器
        
        Returns:
            bool: 是否初始化成功
        """
        try:
            if not self.model_provider_manager.is_initialized:
                success = await self.model_provider_manager.initialize()
                if not success:
                    logger.error("模型提供商管理器初始化失败")
                    return False
            
            self._initialized = True
            logger.info("描述符执行器初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"描述符执行器初始化失败: {str(e)}")
            return False
    
    async def execute_descriptor(
        self,
        descriptor: BaseDescriptor,
        context: ExecutionContext,
        model_name: str = "default",
        provider_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        执行描述符
        
        Args:
            descriptor: 描述符实例
            context: 执行上下文
            model_name: 使用的模型名称
            provider_name: 模型提供商名称，如果为None则使用默认提供商
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        try:
            # 确保执行器已初始化
            if not self._initialized:
                await self.initialize()
            
            # 验证描述符适用性
            if not descriptor.is_applicable(context):
                logger.warning(f"描述符 {descriptor.name} 不适用于当前上下文")
                return {
                    "success": False,
                    "error": "描述符不适用于当前上下文",
                    "descriptor": descriptor.name
                }
            
            # 执行前：更新上下文
            current_step_name = descriptor.name
            context.current_step = current_step_name
            context.update_current_time()

            # 验证上下文
            validation_errors = descriptor.validate_context(context)
            if validation_errors:
                logger.error(f"上下文验证失败: {validation_errors}")
                return {
                    "success": False,
                    "error": f"上下文验证失败: {', '.join(validation_errors)}",
                    "descriptor": descriptor.name
                }
            
            # 准备执行
            preparation_result = descriptor.prepare_execution(context)
            logger.info(f"描述符 {descriptor.name} 准备执行: {preparation_result}")
            
            # 获取执行配置
            execution_config = descriptor.get_execution_config(context)
            
            # 执行描述符
            result = await self._execute_with_agent(descriptor, execution_config, context, model_name, provider_name)
            
            # 后处理
            post_result = descriptor.post_execution(context, result)
            logger.info(f"描述符 {descriptor.name} 执行完成: {post_result}")
            
            result_payload = {
                "success": True,
                "result": result,
                "descriptor": descriptor.name,
                "preparation": preparation_result,
                "post_processing": post_result
            }

            # 执行后：更新上下文
            context.add_completed_step(current_step_name)
            context.set_step_result(current_step_name, result_payload)

            return result_payload
            
        except Exception as e:
            logger.error(f"描述符 {descriptor.name} 执行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "descriptor": descriptor.name
            }
    
    async def _execute_with_agent(
        self,
        descriptor: BaseDescriptor,
        execution_config,
        context: ExecutionContext,
        model_name: str,
        provider_name: str
    ) -> Any:
        """
        使用Agent执行描述符
        
        Args:
            descriptor: 描述符实例
            execution_config: 执行配置
            context: 执行上下文
            model_name: 模型名称
            provider_name: 模型提供商
            
        Returns:
            Any: 执行结果
        """
        # 如果有描述器配置，使用Agent执行
        if execution_config.descriptor_config:
            return await self._execute_with_llm_agent(
                descriptor, execution_config.descriptor_config, context, model_name, provider_name
            )
        
        # 如果只有工具指令，直接执行工具
        if execution_config.tool_instructions:
            return await self._execute_tools_only(execution_config.tool_instructions, context)
        
        # 如果都没有，返回基本信息
        return {
            "type": "basic_execution",
            "step_type": execution_config.step_type,
            "name": execution_config.name,
            "description": execution_config.description
        }
    @retry_async(max_retries=3)
    async def _execute_with_llm_agent(
        self,
        descriptor: BaseDescriptor,
        descriptor_config: DescriptorConfig,
        context: ExecutionContext,
        model_name: str,
        provider_name: str
    ) -> Any:
        """
        使用LLM Agent执行描述符配置
        
        Args:
            descriptor: 描述符实例
            descriptor_config: 描述符配置
            context: 执行上下文
            model_name: 模型名称
            provider_name: 模型提供商
            
        Returns:
            Any: 执行结果
        """
        try:
            # 创建LLM实例
            llm = await self._create_llm(model_name, provider_name)
            if not llm:
                raise RuntimeError(f"无法创建模型实例: {provider_name} {model_name}")
            
            # 封装工具
            tools = self.tool_wrapper.wrap_descriptor_tools(descriptor, context)
            
            # 如果没有工具，直接使用LLM生成内容
            if not tools:
                return await self._execute_llm_only(llm, descriptor_config, context)
            
            # 创建提示模板
            prompt = self._create_prompt_template(descriptor_config)
            
            # 创建调试回调
            callbacks = []
            if self.enable_debug_callback:
                debug_callback = DebugStreamingCallback(enable_debug=True)
                callbacks.append(debug_callback)
                logger.info(f"🔍 启用调试回调 - 描述符: {descriptor.name}")

            # 创建Agent
            agent = create_tool_calling_agent(llm, tools, prompt)
            agent_executor = AgentExecutor(
                agent=agent,
                tools=tools,
                verbose=True,
                max_iterations=10,
                early_stopping_method="generate",
                callbacks=callbacks
            )

            # 准备输入变量
            input_variables = self._prepare_input_variables(descriptor_config, context)

            # 执行Agent
            logger.info(f"🚀 使用Agent执行描述符: {descriptor.name}")
            logger.info(f"📊 模型信息 - 名称: {llm.model_name}, 温度: {llm.temperature}, 最大Token: {llm.max_tokens}")
            logger.info(f"🔧 可用工具: {[tool.name for tool in tools]}")

            result = await agent_executor.ainvoke(input_variables, config={"callbacks": callbacks})
            output = result.get("output", "")
            if not output:
                raise ValueError("Agent执行失败: 未获得有效输出")

            return {
                "type": "agent_execution",
                "output": result.get("output", ""),
                "intermediate_steps": result.get("intermediate_steps", []),
                "tools_used": [tool.name for tool in tools]
            }
            
        except Exception as e:
            logger.error(f"Agent执行失败: {str(e)}")
            raise
    
    async def _execute_llm_only(
        self,
        llm: ChatOpenAI,
        descriptor_config: DescriptorConfig,
        context: ExecutionContext
    ) -> Any:
        """
        仅使用LLM执行（无工具）
        
        Args:
            llm: LLM实例
            descriptor_config: 描述符配置
            context: 执行上下文
            
        Returns:
            Any: 执行结果
        """
        try:
            # 统一从上下文收集变量并生成完整提示词
            variables = self._collect_context_variables(descriptor_config, context)
            full_prompt = descriptor_config.get_full_prompt(variables)

            # 创建调试回调
            callbacks = []
            if self.enable_debug_callback:
                debug_callback = DebugStreamingCallback(enable_debug=True)
                callbacks.append(debug_callback)
                logger.info(f"🔍 启用调试回调 - LLM Only模式")
                logger.info(f"📊 模型信息 - 名称: {llm.model_name}, 温度: {llm.temperature}")

            # 调用LLM
            response = await llm.ainvoke(full_prompt, config={"callbacks": callbacks})
            
            return {
                "type": "llm_only_execution",
                "output": response.content,
                "prompt": full_prompt
            }
            
        except Exception as e:
            logger.error(f"LLM执行失败: {str(e)}")
            raise
    
    async def _execute_tools_only(self, tool_instructions, context: ExecutionContext) -> Any:
        """
        仅执行工具指令（无LLM）
        
        Args:
            tool_instructions: 工具指令列表
            context: 执行上下文
            
        Returns:
            Any: 执行结果
        """
        try:
            # 使用工具管理器执行工具指令
            result = await self.tool_manager.execute_instructions(tool_instructions, context)
            
            return {
                "type": "tools_only_execution",
                "tool_results": result,
                "instructions_count": len(tool_instructions)
            }
            
        except Exception as e:
            logger.error(f"工具执行失败: {str(e)}")
            raise
    
    async def _create_llm(self, model_name: str, provider_name: str):
        """
        创建LLM实例
        
        Args:
            model_name: 模型名称
            provider_name: 模型提供商
            
        Returns:
            LLM实例
        """
        try:
            # 获取模型配置
            model_config = await self.model_provider_manager.get_model(model_name, provider_name)
            if not model_config:
                # 尝试使用默认提供商的模型
                provider = self.model_provider_manager.get_provider(provider_name)
                if provider:
                    models = await provider.get_models()
                    if models:
                        model_config = models[0]  # 使用第一个可用模型
            
            if not model_config:
                raise RuntimeError(f"无法找到模型配置: {model_name}")
            
            # 创建ChatOpenAI实例（兼容大多数提供商）
            llm = ChatOpenAI(
                model=model_config.name,
                api_key=model_config.api_key,
                base_url=model_config.api_base,
                temperature=model_config.temperature,
                max_tokens=model_config.max_tokens,
                openai_proxy=model_config.proxy,
                streaming=model_config.supports_streaming,
                timeout=30,
                max_retries=3
            )
            
            logger.info(f"创建LLM实例成功: {model_config.name}")
            return llm
            
        except Exception as e:
            logger.error(f"创建LLM实例失败: {str(e)}")
            return None

    def _create_prompt_template(self, descriptor_config: DescriptorConfig) -> ChatPromptTemplate:
        """
        创建提示模板

        Args:
            descriptor_config: 描述符配置

        Returns:
            ChatPromptTemplate: 提示模板
        """
        messages = []

        # 仅使用用户消息携带完整提示词，避免与 DescriptorConfig.get_full_prompt 重复
        messages.append(("user", "{input}"))

        # Agent思维链占位符
        messages.append(("assistant", "{agent_scratchpad}"))

        return ChatPromptTemplate.from_messages(messages)

    def _prepare_input_variables(
        self,
        descriptor_config: DescriptorConfig,
        context: ExecutionContext
    ) -> Dict[str, Any]:
        """
        准备输入变量

        Args:
            descriptor_config: 描述符配置
            context: 执行上下文

        Returns:
            Dict[str, Any]: 输入变量字典
        """
        # 收集上下文变量并生成完整提示词
        template_vars = self._collect_context_variables(descriptor_config, context)
        full_prompt = descriptor_config.get_full_prompt(template_vars)

        # 仅暴露给 Agent 所需的 input（避免不必要的变量泄露）
        return {"input": full_prompt}

    def _collect_context_variables(
        self,
        descriptor_config: DescriptorConfig,
        context: ExecutionContext
    ) -> Dict[str, Any]:
        """
        从 ExecutionContext 统一收集模板变量，避免对 pydantic 对象使用 hasattr/getattr。

        优先级：
        - 上下文字段（model_dump）
        - 执行结果 execution_results 覆盖
        - 针对 descriptor_config.input_variables 的显式补充（如果上述都没有，则从 get_variable 获取，最后兜底为占位文本）
        """
        # 1) 基础上下文字段
        ctx_dict = context.model_dump()
        variables: Dict[str, Any] = dict(ctx_dict)

        # 2) 执行结果覆盖
        variables.update(context.execution_results)

        # 3) 补齐配置声明的输入变量
        for var_name in descriptor_config.input_variables:
            if var_name not in variables or variables[var_name] is None:
                value = context.get_variable(var_name, None)
                variables[var_name] = value if value is not None else f"${{{var_name}}}"

        return variables

    async def execute_multiple_descriptors(
        self,
        descriptors: List[BaseDescriptor],
        context: ExecutionContext,
        model_name: str = "default",
        provider_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        批量执行描述符

        Args:
            descriptors: 描述符列表
            context: 执行上下文
            model_name: 模型名称
            provider_name: 模型提供商名称，如果为None则使用默认提供商

        Returns:
            Dict[str, Any]: 批量执行结果
        """
        results = {}
        errors = []

        # 按优先级排序
        sorted_descriptors = sorted(
            descriptors,
            key=lambda d: d.get_priority(context),
            reverse=True
        )

        for descriptor in sorted_descriptors:
            try:
                result = await self.execute_descriptor(descriptor, context, model_name, provider_name)
                results[descriptor.name] = result

                if not result.get("success", True):
                    errors.append(f"{descriptor.name}: {result.get('error', '未知错误')}")

            except Exception as e:
                error_msg = f"{descriptor.name}: {str(e)}"
                errors.append(error_msg)
                results[descriptor.name] = {
                    "success": False,
                    "error": str(e),
                    "descriptor": descriptor.name
                }

        return {
            "total": len(descriptors),
            "completed": len(results),
            "errors": errors,
            "results": results,
            "success": len(errors) == 0
        }
