"""
文件操作工具处理器
"""

import os
import re
import logging
from pathlib import Path
from typing import Dict, Any, List, Type, Optional
from pydantic import BaseModel, Field

from ...models.context import ExecutionContext
from ..base_handler import BaseToolHandler

logger = logging.getLogger(__name__)


class ViewFileInput(BaseModel):
    """文件查看工具输入参数"""
    path: str = Field(description="文件路径")
    view_range: Optional[List[int]] = Field(description="查看范围，格式为 [start_line, end_line]", default=None)
    search_query_regex: Optional[str] = Field(description="正则表达式搜索模式", default=None)
    case_sensitive: Optional[bool] = Field(description="是否区分大小写", default=False)
    context_lines_before: Optional[int] = Field(description="匹配前显示的行数", default=5)
    context_lines_after: Optional[int] = Field(description="匹配后显示的行数", default=5)


class ViewDirectoryInput(BaseModel):
    """目录查看工具输入参数"""
    path: str = Field(description="目录路径")


class SaveFileInput(BaseModel):
    """文件保存工具输入参数"""
    path: str = Field(description="文件路径")
    file_content: str = Field(description="文件内容")
    instructions_reminder: str = Field(description="指令提醒", default="LIMIT THE FILE CONTENT TO AT MOST 300 LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED.")
    add_last_line_newline: Optional[bool] = Field(description="是否在文件末尾添加换行符", default=True)


class RemoveFilesInput(BaseModel):
    """文件删除工具输入参数"""
    file_paths: List[str] = Field(description="要删除的文件路径列表")


class StrReplaceEditorInput(BaseModel):
    """字符串替换编辑器工具输入参数"""
    command: str = Field(description="命令类型，'str_replace' 或 'insert'")
    path: str = Field(description="文件路径")
    instruction_reminder: str = Field(description="指令提醒", default="ALWAYS BREAK DOWN EDITS INTO SMALLER CHUNKS OF AT MOST 150 LINES EACH.")
    old_str_1: Optional[str] = Field(description="要替换的旧字符串", default=None)
    new_str_1: Optional[str] = Field(description="新字符串", default=None)
    old_str_start_line_number_1: Optional[int] = Field(description="旧字符串开始行号", default=None)
    old_str_end_line_number_1: Optional[int] = Field(description="旧字符串结束行号", default=None)
    insert_line_1: Optional[int] = Field(description="插入位置行号", default=None)


class ViewFileHandler(BaseToolHandler):
    """文件查看工具处理器"""

    def __init__(self):
        super().__init__("view-file")

    def get_args_schema(self) -> Type[BaseModel]:
        """获取参数模型"""
        return ViewFileInput

    async def execute(self, parameters: Dict[str, Any], context: ExecutionContext) -> Any:
        """执行文件查看"""
        path = parameters.get('path', '')
        view_range = parameters.get('view_range')
        search_query_regex = parameters.get('search_query_regex')
        case_sensitive = parameters.get('case_sensitive', False)
        context_lines_before = parameters.get('context_lines_before', 5)
        context_lines_after = parameters.get('context_lines_after', 5)

        logger.info(f"查看文件: {path}")

        try:
            # 解析路径
            file_path = Path(path)
            if not file_path.is_absolute():
                # 相对于工作空间路径
                workspace_path = Path(context.workspace_path) if context.workspace_path else Path.cwd()
                file_path = workspace_path / file_path

            # 检查文件是否存在
            if not file_path.exists():
                return {
                    "path": str(file_path),
                    "error": f"文件不存在: {file_path}",
                    "timestamp": context.current_time
                }

            if not file_path.is_file():
                return {
                    "path": str(file_path),
                    "error": f"路径不是文件: {file_path}",
                    "timestamp": context.current_time
                }

            # 读取文件内容
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
            except UnicodeDecodeError:
                # 尝试其他编码
                try:
                    with open(file_path, 'r', encoding='gbk') as f:
                        lines = f.readlines()
                except UnicodeDecodeError:
                    return {
                        "path": str(file_path),
                        "error": "无法读取文件，编码不支持",
                        "timestamp": context.current_time
                    }

            # 处理查看范围
            if view_range and len(view_range) == 2:
                start_line, end_line = view_range
                start_line = max(1, start_line) - 1  # 转换为0索引
                end_line = min(len(lines), end_line) if end_line > 0 else len(lines)
                lines = lines[start_line:end_line]
                line_offset = start_line
            else:
                line_offset = 0

            # 处理正则搜索
            if search_query_regex:
                try:
                    pattern = re.compile(search_query_regex, 0 if case_sensitive else re.IGNORECASE)
                    matched_lines = []

                    for i, line in enumerate(lines):
                        if pattern.search(line):
                            # 添加上下文行
                            start_ctx = max(0, i - context_lines_before)
                            end_ctx = min(len(lines), i + context_lines_after + 1)

                            for j in range(start_ctx, end_ctx):
                                line_num = j + line_offset + 1
                                is_match = j == i
                                matched_lines.append({
                                    "line_number": line_num,
                                    "content": lines[j].rstrip('\n\r'),
                                    "is_match": is_match
                                })

                    return {
                        "path": str(file_path),
                        "type": "file",
                        "search_results": matched_lines,
                        "total_matches": sum(1 for line in matched_lines if line["is_match"]),
                        "timestamp": context.current_time
                    }
                except re.error as e:
                    return {
                        "path": str(file_path),
                        "error": f"正则表达式错误: {e}",
                        "timestamp": context.current_time
                    }

            # 返回完整内容
            content_lines = []
            for i, line in enumerate(lines):
                content_lines.append({
                    "line_number": i + line_offset + 1,
                    "content": line.rstrip('\n\r')
                })

            return {
                "path": str(file_path),
                "type": "file",
                "lines": content_lines,
                "total_lines": len(lines),
                "timestamp": context.current_time
            }

        except Exception as e:
            logger.error(f"读取文件失败 {path}: {e}")
            return {
                "path": path,
                "error": f"读取文件失败: {str(e)}",
                "timestamp": context.current_time
            }

    def validate_parameters(self, parameters: Dict[str, Any]) -> List[str]:
        """验证参数"""
        errors = []
        if not parameters.get('path'):
            errors.append("缺少必需参数: path")
        return errors


class ViewDirectoryHandler(BaseToolHandler):
    """目录查看工具处理器"""

    def __init__(self):
        super().__init__("view-directory")

    def get_args_schema(self) -> Type[BaseModel]:
        """获取参数模型"""
        return ViewDirectoryInput

    async def execute(self, parameters: Dict[str, Any], context: ExecutionContext) -> Any:
        """执行目录查看"""
        path = parameters.get('path', '')

        logger.info(f"查看目录: {path}")

        try:
            # 解析路径
            dir_path = Path(path)
            if not dir_path.is_absolute():
                # 相对于工作空间路径
                workspace_path = Path(context.workspace_path) if context.workspace_path else Path.cwd()
                dir_path = workspace_path / dir_path

            # 检查目录是否存在
            if not dir_path.exists():
                return {
                    "path": str(dir_path),
                    "error": f"目录不存在: {dir_path}",
                    "timestamp": context.current_time
                }

            if not dir_path.is_dir():
                return {
                    "path": str(dir_path),
                    "error": f"路径不是目录: {dir_path}",
                    "timestamp": context.current_time
                }

            # 生成树结构
            tree_content = await self._generate_tree_structure(dir_path)

            return {
                "path": str(dir_path),
                "type": "directory",
                "content": tree_content,
                "timestamp": context.current_time
            }

        except Exception as e:
            logger.error(f"读取目录失败 {path}: {e}")
            return {
                "path": path,
                "error": f"读取目录失败: {str(e)}",
                "timestamp": context.current_time
            }

    async def _generate_tree_structure(self, dir_path: Path, max_depth: int = 3) -> str:
        """生成目录树结构字符串"""
        def _build_tree(current_path: Path, prefix: str = "", depth: int = 0) -> List[str]:
            """递归构建树结构"""
            if depth > max_depth:
                return [f"{prefix}... (深度限制)"]

            lines = []
            try:
                # 获取目录内容并排序
                items = sorted(current_path.iterdir(), key=lambda x: (not x.is_dir(), x.name.lower()))

                for i, item in enumerate(items):
                    is_last = i == len(items) - 1
                    current_prefix = "└── " if is_last else "├── "
                    next_prefix = "    " if is_last else "│   "

                    try:
                        if item.is_dir():
                            # 目录
                            try:
                                child_count = len(list(item.iterdir()))
                                dir_info = f"📁 {item.name}/ ({child_count} items)"
                            except PermissionError:
                                dir_info = f"📁 {item.name}/ (无权限)"

                            lines.append(f"{prefix}{current_prefix}{dir_info}")

                            # 递归处理子目录（限制深度）
                            if depth < max_depth:
                                try:
                                    sub_lines = _build_tree(item, prefix + next_prefix, depth + 1)
                                    lines.extend(sub_lines)
                                except PermissionError:
                                    lines.append(f"{prefix}{next_prefix}(无权限访问)")
                        else:
                            # 文件
                            try:
                                file_size = item.stat().st_size
                                if file_size < 1024:
                                    size_str = f"{file_size}B"
                                elif file_size < 1024 * 1024:
                                    size_str = f"{file_size/1024:.1f}KB"
                                else:
                                    size_str = f"{file_size/(1024*1024):.1f}MB"

                                file_info = f"📄 {item.name} ({size_str})"
                            except (PermissionError, OSError):
                                file_info = f"📄 {item.name} (无法获取信息)"

                            lines.append(f"{prefix}{current_prefix}{file_info}")

                    except (PermissionError, OSError) as e:
                        lines.append(f"{prefix}{current_prefix}❌ {item.name} (错误: {str(e)})")

            except PermissionError:
                lines.append(f"{prefix}(无权限访问此目录)")
            except Exception as e:
                lines.append(f"{prefix}(错误: {str(e)})")

            return lines

        # 构建树结构
        tree_lines = [f"📁 {dir_path.name}/"]
        tree_lines.extend(_build_tree(dir_path))

        # 添加统计信息
        try:
            total_items = 0
            file_count = 0
            dir_count = 0

            for item in dir_path.rglob("*"):
                if item.is_file():
                    file_count += 1
                elif item.is_dir():
                    dir_count += 1
                total_items += 1

                # 限制统计深度，避免大目录统计过慢
                if total_items > 1000:
                    break

            tree_lines.append("")
            if total_items > 1000:
                tree_lines.append(f"📊 统计信息: {dir_count}+ 个目录, {file_count}+ 个文件 (超过1000项，统计截断)")
            else:
                tree_lines.append(f"📊 统计信息: {dir_count} 个目录, {file_count} 个文件")

        except Exception:
            tree_lines.append("")
            tree_lines.append("📊 统计信息: 无法获取")

        return "\n".join(tree_lines)

    def validate_parameters(self, parameters: Dict[str, Any]) -> List[str]:
        """验证参数"""
        errors = []
        if not parameters.get('path'):
            errors.append("缺少必需参数: path")
        return errors


class SaveFileHandler(BaseToolHandler):
    """文件保存工具处理器"""

    def __init__(self):
        super().__init__("save-file")

    def get_args_schema(self) -> Type[BaseModel]:
        """获取参数模型"""
        return SaveFileInput

    async def execute(self, parameters: Dict[str, Any], context: ExecutionContext) -> Any:
        """执行文件保存"""
        path = parameters.get('path', '')
        file_content = parameters.get('file_content', '')
        add_last_line_newline = parameters.get('add_last_line_newline', True)

        logger.info(f"保存文件: {path}")

        try:
            # 解析路径
            file_path = Path(path)
            if not file_path.is_absolute():
                # 相对于工作空间路径
                workspace_path = Path(context.workspace_path) if context.workspace_path else Path.cwd()
                file_path = workspace_path / file_path

            # 创建父目录（如果不存在）
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # 处理换行符
            content_to_write = file_content
            if add_last_line_newline and content_to_write and not content_to_write.endswith('\n'):
                content_to_write += '\n'

            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content_to_write)

            # 获取文件信息
            file_stat = file_path.stat()

            return {
                "path": str(file_path),
                "content_length": len(file_content),
                "file_size": file_stat.st_size,
                "lines_count": len(file_content.splitlines()),
                "message": f"文件保存成功: {file_path.name}",
                "timestamp": context.current_time
            }

        except PermissionError:
            return {
                "path": path,
                "error": "没有权限写入文件",
                "timestamp": context.current_time
            }
        except OSError as e:
            return {
                "path": path,
                "error": f"文件系统错误: {str(e)}",
                "timestamp": context.current_time
            }
        except Exception as e:
            logger.error(f"保存文件失败 {path}: {e}")
            return {
                "path": path,
                "error": f"保存文件失败: {str(e)}",
                "timestamp": context.current_time
            }

    def validate_parameters(self, parameters: Dict[str, Any]) -> List[str]:
        """验证参数"""
        errors = []
        if not parameters.get('path'):
            errors.append("缺少必需参数: path")
        if not parameters.get('file_content'):
            errors.append("缺少必需参数: file_content")
        return errors


class RemoveFilesHandler(BaseToolHandler):
    """文件删除工具处理器"""

    def __init__(self):
        super().__init__("remove-files")

    def get_args_schema(self) -> Type[BaseModel]:
        """获取参数模型"""
        return RemoveFilesInput

    async def execute(self, parameters: Dict[str, Any], context: ExecutionContext) -> Any:
        """执行文件删除"""
        file_paths = parameters.get('file_paths', [])

        logger.info(f"删除文件: {file_paths}")

        if not file_paths:
            return {
                "file_paths": [],
                "error": "没有指定要删除的文件",
                "timestamp": context.current_time
            }

        deleted_files = []
        failed_files = []

        for path_str in file_paths:
            try:
                # 解析路径
                file_path = Path(path_str)
                if not file_path.is_absolute():
                    # 相对于工作空间路径
                    workspace_path = Path(context.workspace_path) if context.workspace_path else Path.cwd()
                    file_path = workspace_path / file_path

                # 检查文件是否存在
                if not file_path.exists():
                    failed_files.append({
                        "path": str(file_path),
                        "error": "文件不存在"
                    })
                    continue

                # 删除文件或目录
                if file_path.is_file():
                    file_path.unlink()
                    deleted_files.append({
                        "path": str(file_path),
                        "type": "file"
                    })
                elif file_path.is_dir():
                    # 删除目录（包括内容）
                    import shutil
                    shutil.rmtree(file_path)
                    deleted_files.append({
                        "path": str(file_path),
                        "type": "directory"
                    })
                else:
                    failed_files.append({
                        "path": str(file_path),
                        "error": "未知文件类型"
                    })

            except PermissionError:
                failed_files.append({
                    "path": path_str,
                    "error": "没有权限删除"
                })
            except OSError as e:
                failed_files.append({
                    "path": path_str,
                    "error": f"文件系统错误: {str(e)}"
                })
            except Exception as e:
                logger.error(f"删除文件失败 {path_str}: {e}")
                failed_files.append({
                    "path": path_str,
                    "error": f"删除失败: {str(e)}"
                })

        return {
            "requested_paths": file_paths,
            "deleted_files": deleted_files,
            "failed_files": failed_files,
            "deleted_count": len(deleted_files),
            "failed_count": len(failed_files),
            "message": f"删除完成: 成功 {len(deleted_files)} 个，失败 {len(failed_files)} 个",
            "timestamp": context.current_time
        }

    def validate_parameters(self, parameters: Dict[str, Any]) -> List[str]:
        """验证参数"""
        errors = []
        if not parameters.get('file_paths'):
            errors.append("缺少必需参数: file_paths")
        elif not isinstance(parameters.get('file_paths'), list):
            errors.append("file_paths参数必须是列表")
        return errors


class StrReplaceEditorHandler(BaseToolHandler):
    """文件编辑工具处理器"""

    def __init__(self):
        super().__init__("str-replace-editor")

    def get_args_schema(self) -> Type[BaseModel]:
        """获取参数模型"""
        return StrReplaceEditorInput

    async def execute(self, parameters: Dict[str, Any], context: ExecutionContext) -> Any:
        """执行文件编辑"""
        path = parameters.get('path', '')
        command = parameters.get('command', '')

        logger.info(f"编辑文件: {path} ({command})")

        try:
            # 解析路径
            file_path = Path(path)
            if not file_path.is_absolute():
                # 相对于工作空间路径
                workspace_path = Path(context.workspace_path) if context.workspace_path else Path.cwd()
                file_path = workspace_path / file_path

            # 检查文件是否存在
            if not file_path.exists():
                return {
                    "path": str(file_path),
                    "error": f"文件不存在: {file_path}",
                    "timestamp": context.current_time
                }

            if not file_path.is_file():
                return {
                    "path": str(file_path),
                    "error": f"路径不是文件: {file_path}",
                    "timestamp": context.current_time
                }

            # 读取文件内容
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.splitlines(keepends=True)
            except UnicodeDecodeError:
                try:
                    with open(file_path, 'r', encoding='gbk') as f:
                        content = f.read()
                        lines = content.splitlines(keepends=True)
                except UnicodeDecodeError:
                    return {
                        "path": str(file_path),
                        "error": "无法读取文件，编码不支持",
                        "timestamp": context.current_time
                    }

            # 执行编辑操作
            if command == 'str_replace':
                result = await self._handle_str_replace(parameters, lines, file_path)
            elif command == 'insert':
                result = await self._handle_insert(parameters, lines, file_path)
            else:
                return {
                    "path": str(file_path),
                    "error": f"不支持的命令: {command}",
                    "timestamp": context.current_time
                }

            if result.get('error'):
                return result

            # 写入修改后的内容
            new_content = ''.join(result['new_lines'])
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)

            return {
                "path": str(file_path),
                "command": command,
                "changes": result['changes'],
                "message": f"文件编辑成功: {len(result['changes'])} 处修改",
                "timestamp": context.current_time
            }

        except Exception as e:
            logger.error(f"编辑文件失败 {path}: {e}")
            return {
                "path": path,
                "error": f"编辑文件失败: {str(e)}",
                "timestamp": context.current_time
            }

    async def _handle_str_replace(self, parameters: Dict[str, Any], lines: List[str], file_path: Path) -> Dict[str, Any]:
        """处理字符串替换"""
        old_str = parameters.get('old_str_1', '')
        new_str = parameters.get('new_str_1', '')
        start_line = parameters.get('old_str_start_line_number_1')
        end_line = parameters.get('old_str_end_line_number_1')

        if not old_str:
            return {
                "error": "str_replace命令缺少old_str_1参数",
                "timestamp": None
            }

        # 如果指定了行号范围，在该范围内查找
        if start_line is not None and end_line is not None:
            start_idx = max(0, start_line - 1)  # 转换为0索引
            end_idx = min(len(lines), end_line)

            # 提取指定范围的内容
            range_content = ''.join(lines[start_idx:end_idx])

            if old_str not in range_content:
                return {
                    "error": f"在指定行范围 {start_line}-{end_line} 中未找到要替换的字符串",
                    "timestamp": None
                }

            # 执行替换
            new_range_content = range_content.replace(old_str, new_str, 1)  # 只替换第一个匹配
            new_range_lines = new_range_content.splitlines(keepends=True)

            # 重构完整的行列表
            new_lines = lines[:start_idx] + new_range_lines + lines[end_idx:]

            changes = [{
                "type": "str_replace",
                "line_range": f"{start_line}-{end_line}",
                "old_str": old_str,
                "new_str": new_str
            }]
        else:
            # 在整个文件中查找并替换
            full_content = ''.join(lines)
            if old_str not in full_content:
                return {
                    "error": "未找到要替换的字符串",
                    "timestamp": None
                }

            new_content = full_content.replace(old_str, new_str, 1)  # 只替换第一个匹配
            new_lines = new_content.splitlines(keepends=True)

            changes = [{
                "type": "str_replace",
                "old_str": old_str,
                "new_str": new_str
            }]

        return {
            "new_lines": new_lines,
            "changes": changes
        }

    async def _handle_insert(self, parameters: Dict[str, Any], lines: List[str], file_path: Path) -> Dict[str, Any]:
        """处理插入操作"""
        insert_line = parameters.get('insert_line_1')
        new_str = parameters.get('new_str_1', '')

        if insert_line is None:
            return {
                "error": "insert命令缺少insert_line_1参数",
                "timestamp": None
            }

        # 转换为0索引
        insert_idx = max(0, insert_line)

        # 确保插入的字符串以换行符结尾（如果不是空字符串）
        if new_str and not new_str.endswith('\n'):
            new_str += '\n'

        # 插入新内容
        new_lines = lines[:insert_idx] + [new_str] + lines[insert_idx:]

        changes = [{
            "type": "insert",
            "line": insert_line,
            "content": new_str.rstrip('\n')
        }]

        return {
            "new_lines": new_lines,
            "changes": changes
        }

    def validate_parameters(self, parameters: Dict[str, Any]) -> List[str]:
        """验证参数"""
        errors = []
        if not parameters.get('path'):
            errors.append("缺少必需参数: path")
        if not parameters.get('command'):
            errors.append("缺少必需参数: command")

        command = parameters.get('command')
        if command == 'str_replace':
            if not parameters.get('old_str_1'):
                errors.append("str_replace命令缺少old_str_1参数")
        elif command == 'insert':
            if parameters.get('insert_line_1') is None:
                errors.append("insert命令缺少insert_line_1参数")
        elif command:
            errors.append(f"不支持的命令: {command}")

        return errors

