"""
模型配置处理器
"""

import json
from typing import Dict, Any, Optional
from tornado.web import HTTPError
import structlog
from dependency_injector.wiring import inject, Provide

from app.api.base import BaseHandler
from app.core.di.containers import Container
from app.services.model_providers.manager import ModelProviderManager

logger = structlog.get_logger(__name__)


class ModelConfigHandler(BaseHandler):
    """模型配置处理器"""

    @inject
    def initialize(
        self,
        model_provider_manager: ModelProviderManager = Provide[Container.model_provider_manager],
    ):
        super().initialize()
        self.model_provider_manager = model_provider_manager

    async def get(self):
        """获取可用模型列表"""
        try:
            # 确保模型提供商管理器已初始化
            if not self.model_provider_manager.is_initialized:
                await self.model_provider_manager.initialize()

            # 获取模型列表
            model_list_response = await self.model_provider_manager.list_models()

            self.success_response({
                "models": model_list_response.model_dump()
            }, "模型列表获取成功")

        except Exception as e:
            logger.error("获取模型列表失败", error=str(e), exc_info=e)
            raise HTTPError(500, f"获取模型列表失败: {str(e)}")
