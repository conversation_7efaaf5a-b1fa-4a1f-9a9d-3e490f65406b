"""
执行上下文模型

定义描述器执行过程中的上下文信息
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from datetime import datetime, timezone


class ExecutionContext(BaseModel):
    """执行上下文"""
    
    # 基本信息
    process_id: str = Field(..., description="流程ID")
    process_name: str = Field(..., description="流程名称")
    process_type: str = Field(..., description="流程类型")
    
    # 执行环境
    workspace_path: Optional[str] = Field(default=None, description="工作空间路径")
    environment: Dict[str, Any] = Field(default_factory=dict, description="环境变量")
    
    # 用户输入
    user_requirements: Optional[str] = Field(default=None, description="用户需求")
    user_preferences: Dict[str, Any] = Field(default_factory=dict, description="用户偏好")
    
    # 执行状态
    current_step: Optional[str] = Field(default=None, description="当前步骤")
    completed_steps: List[str] = Field(default_factory=list, description="已完成步骤")
    execution_results: Dict[str, Any] = Field(default_factory=dict, description="执行结果")
    
    # 时间信息
    start_time: Optional[datetime] = Field(default=None, description="开始时间")
    current_time: Optional[datetime] = Field(default=None, description="当前时间")
    
    # 配置信息
    global_config: Dict[str, Any] = Field(default_factory=dict, description="全局配置")
    step_configs: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="步骤配置")
    
    model_config = {"ser_json_timedelta": "iso8601"}
    
    def get_variable(self, name: str, default: Any = None) -> Any:
        """
        获取上下文变量
        
        Args:
            name: 变量名
            default: 默认值
            
        Returns:
            Any: 变量值
        """
        # 优先级：执行结果 > 用户偏好 > 环境变量 > 全局配置
        if name in self.execution_results:
            return self.execution_results[name]
        elif name in self.user_preferences:
            return self.user_preferences[name]
        elif name in self.environment:
            return self.environment[name]
        elif name in self.global_config:
            return self.global_config[name]
        else:
            return default
    
    def set_variable(self, name: str, value: Any) -> None:
        """
        设置上下文变量
        
        Args:
            name: 变量名
            value: 变量值
        """
        self.execution_results[name] = value
    
    def add_completed_step(self, step_name: str) -> None:
        """
        添加已完成步骤
        
        Args:
            step_name: 步骤名称
        """
        if step_name not in self.completed_steps:
            self.completed_steps.append(step_name)
    
    def is_step_completed(self, step_name: str) -> bool:
        """
        检查步骤是否已完成
        
        Args:
            step_name: 步骤名称
            
        Returns:
            bool: 是否已完成
        """
        return step_name in self.completed_steps
    
    def get_step_result(self, step_name: str, default: Any = None) -> Any:
        """
        获取步骤执行结果
        
        Args:
            step_name: 步骤名称
            default: 默认值
            
        Returns:
            Any: 步骤结果
        """
        return self.execution_results.get(step_name, default)
    
    def set_step_result(self, step_name: str, result: Any) -> None:
        """
        设置步骤执行结果
        
        Args:
            step_name: 步骤名称
            result: 执行结果
        """
        self.execution_results[step_name] = result
    
    def update_current_time(self) -> None:
        """更新当前时间"""
        self.current_time = datetime.now(timezone.utc)
    
    def get_execution_duration(self) -> Optional[float]:
        """
        获取执行时长（秒）
        
        Returns:
            Optional[float]: 执行时长
        """
        if self.start_time and self.current_time:
            return (self.current_time - self.start_time).total_seconds()
        return None
    



class ExecutionContextBuilder:
    """执行上下文构建器"""
    
    @staticmethod
    def create_basic_context(
        process_id: str,
        process_name: str,
        process_type: str,
        workspace_path: Optional[str] = None
    ) -> ExecutionContext:
        """创建基础上下文"""
        return ExecutionContext(
            process_id=process_id,
            process_name=process_name,
            process_type=process_type,
            workspace_path=workspace_path,
            start_time=datetime.now(timezone.utc),
            current_time=datetime.now(timezone.utc)
        )
    
    @staticmethod
    def create_with_requirements(
        process_id: str,
        process_name: str,
        process_type: str,
        user_requirements: str,
        workspace_path: Optional[str] = None,
        user_preferences: Optional[Dict[str, Any]] = None
    ) -> ExecutionContext:
        """创建带需求的上下文"""
        return ExecutionContext(
            process_id=process_id,
            process_name=process_name,
            process_type=process_type,
            workspace_path=workspace_path,
            user_requirements=user_requirements,
            user_preferences=user_preferences or {},
            start_time=datetime.now(timezone.utc),
            current_time=datetime.now(timezone.utc)
        )
    
    @staticmethod
    def from_dict(data: Dict[str, Any]) -> ExecutionContext:
        """从字典创建上下文"""
        # 处理时间字段
        if data.get("start_time"):
            data["start_time"] = datetime.fromisoformat(data["start_time"])
        if data.get("current_time"):
            data["current_time"] = datetime.fromisoformat(data["current_time"])
        
        return ExecutionContext(**data)
