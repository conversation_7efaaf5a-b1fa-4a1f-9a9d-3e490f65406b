# 向量存储配置文件

# 默认向量存储
default_store: "chroma_quality"

# 向量存储配置
vector_stores:
  # ChromaDB存储 - 用于数据质量计算
  chroma_quality:
    provider: "chroma"
    collection_name: "data_quality_vectors"
    dimensions: 384  # 与嵌入器维度匹配
    similarity_metric: "cosine"
    persist_directory: "./data/chroma/quality"
    batch_size: 100
    timeout: 30
    extra_params:
      anonymized_telemetry: false
      allow_reset: true

  # FAISS存储 - 高性能搜索
  faiss_quality:
    provider: "faiss"
    collection_name: "quality_index"
    dimensions: 384
    similarity_metric: "cosine"
    persist_directory: "./data/faiss/quality"
    batch_size: 200
    timeout: 30
    extra_params:
      index_type: "IndexFlatIP"  # 内积索引，适合余弦相似度
      nlist: 100
      nprobe: 10

  # FAISS存储 - IVF索引，适合大规模数据
  faiss_large:
    provider: "faiss"
    collection_name: "large_index"
    dimensions: 768
    similarity_metric: "cosine"
    persist_directory: "./data/faiss/large"
    batch_size: 500
    timeout: 60
    extra_params:
      index_type: "IndexIVFFlat"
      nlist: 1000
      nprobe: 50

  # # Milvus存储 - 高性能分布式向量数据库
  milvus_quality:
    provider: "milvus"
    collection_name: "quality_vectors"
    dimensions: 384                  # embedding 向量维度
    similarity_metric: "cosine"      # 相似度度量，可选 L2 / cosine / IP
    host: "milvus"                   # 如果是 docker/k8s，写服务名，不要用 localhost
    port: 19530
    batch_size: 2000                 # 插入批量，推荐 1000~5000
    timeout: 120                     # 大规模检索给足超时时间
    extra_params:
      index_type: "IVF_SQ8"          # 索引类型：大规模推荐 IVF_SQ8 / HNSW
      nlist: 50000                   # IVF 聚类数，≈ sqrt(N)，N=向量条数
      nprobe: 1000                   # 搜索时探测的簇数，推荐 nlist 的 1%~5%

    # # Pinecone存储 - 云端托管向量数据库
    # pinecone_quality:
    #   provider: "pinecone"
    #   collection_name: "quality-index"
    #   dimensions: 384
    #   similarity_metric: "cosine"
    #   api_key: "${PINECONE_API_KEY}"  # 从环境变量读取
    #   batch_size: 100
    #   timeout: 30
    #   extra_params:
    #     environment: "us-east-1-aws"  # Pinecone环境
    #     cloud: "aws"
    #     region: "us-east-1"

    # # Weaviate存储 - 开源向量搜索引擎
    # weaviate_content:
    #   provider: "weaviate"
    #   collection_name: "ContentVectors"
    #   dimensions: 768
    #   similarity_metric: "cosine"
    #   host: "localhost"
    #   port: 8080
    #   batch_size: 50
    #   timeout: 30

    # # Qdrant存储 - 高性能向量相似性搜索引擎
    # qdrant_content:
    #   provider: "qdrant"
    #   collection_name: "content_vectors"
    #   dimensions: 768
    #   similarity_metric: "cosine"
    #   host: "localhost"
    #   port: 6333
    #   batch_size: 50
    #   timeout: 30
    #   extra_params:
    #     use_async: true

  # CodeBase专用存储
  chroma_codebase:
    provider: "chroma"
    collection_name: "codebase_vectors"
    dimensions: 512  # 与本地中文嵌入器维度匹配
    similarity_metric: "cosine"
    persist_directory: "./data/chroma/codebase"
    batch_size: 100
    timeout: 60
    extra_params:
      anonymized_telemetry: false
      allow_reset: true
