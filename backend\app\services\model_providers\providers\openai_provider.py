"""
OpenAI模型提供商
"""

from typing import List
import structlog

from ..base import BaseModelProvider
from ..models import (
    ModelConfig, ModelProviderConfig, ModelCapability, 
    ModelStatus, ModelProviderType
)

logger = structlog.get_logger(__name__)


class OpenAIProvider(BaseModelProvider):
    """OpenAI模型提供商"""
    
    async def _initialize(self) -> None:
        """初始化OpenAI提供商"""
        # 验证API密钥
        if not self.config.api_key:
            raise ValueError("OpenAI API密钥未配置")
        
        # 设置默认API基础URL
        if not self.config.api_base:
            self.config.api_base = "https://api.openai.com/v1"
        
        logger.info(f"OpenAI提供商初始化完成，API基础URL: {self.config.api_base}")
    
    async def _get_models(self) -> List[ModelConfig]:
        """获取OpenAI模型列表"""
        # 只从配置中获取模型，不使用硬编码
        if not self.config.models:
            logger.warning("OpenAI提供商配置中没有定义模型")
            return []

        return self.config.models.copy()
    
    async def _validate_model(self, model: ModelConfig) -> bool:
        """验证OpenAI模型"""
        # 这里可以实现具体的模型验证逻辑
        # 例如发送一个简单的请求来测试模型是否可用
        return model.status == ModelStatus.AVAILABLE
