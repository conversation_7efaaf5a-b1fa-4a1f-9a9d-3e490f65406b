"""
部署说明生成描述器

通过提示词指导LLM生成项目部署和运维说明
"""

import logging
from typing import List

from ...execution_framework.base.step_type import StepType
from ...execution_framework.models.context import ExecutionContext
from ...execution_framework.models.tool_instruction import ToolInstruction
from .base_generation_descriptor import BaseGenerationDescriptor

logger = logging.getLogger(__name__)


class DeploymentDescriptor(BaseGenerationDescriptor):
    """项目部署说明生成描述器 - 指导LLM生成部署运维指南"""

    def __init__(self):
        """初始化部署说明生成描述器"""
        super().__init__(StepType.DEPLOYMENT, "deployment")

    def get_description(self) -> str:
        """获取描述器描述"""
        return "指导LLM生成项目部署、运维和生产环境配置说明"

    def get_tool_instructions(self, context: ExecutionContext) -> List[ToolInstruction]:
        """获取工具调用指令列表 - 每个工具只定义一次"""
        return [
            ToolInstruction(
                name="codebase-retrieval",
                parameters={
                    "information_request": "部署配置、容器化、CI/CD流程、环境变量、生产配置、监控日志"
                },
                description="获取项目部署相关信息",
                result_key="codebase_analysis"
            ),
            ToolInstruction(
                name="view-file",
                parameters={"path": "Dockerfile"},
                description="查看部署配置文件（可多次调用查看不同文件）",
                result_key="deployment_configs"
            ),
            ToolInstruction(
                name="view-directory",
                parameters={"path": ".github/workflows"},
                description="查看CI/CD和部署相关目录",
                result_key="cicd_directory"
            )
        ]

    def get_prompt_template(self) -> str:
        """获取提示词模板 - 指导LLM如何生成部署说明"""
        return """
## 项目信息
项目名称: {project_name}
项目路径: {project_path}
项目类型: {project_type}
编程语言: {programming_languages}
技术框架: {frameworks}

## 代码库分析
{codebase_analysis}

## 部署配置文件
{deployment_configs}

## CI/CD目录信息
{cicd_directory}

## 自定义要求
{custom_prompt}

## 生成任务

请基于以上项目信息，使用可用工具深入分析后，生成详细的部署说明。

### 工具使用指导：
1. 使用 codebase-retrieval 获取部署相关配置和流程
2. 使用 view-file 查看多个部署配置文件：
   - Dockerfile（容器化配置）
   - docker-compose.yml（多容器编排）
   - k8s/*.yaml（Kubernetes配置）
   - deploy.sh（部署脚本）
   - .env.example（环境变量示例）
3. 使用 view-directory 查看CI/CD相关目录：
   - .github/workflows/（GitHub Actions）
   - .gitlab-ci.yml（GitLab CI）
   - jenkins/（Jenkins配置）

### 重点包含：
1. 部署环境要求和准备工作
2. 容器化部署方案（Docker/K8s等）
3. 传统部署方式和步骤
4. 环境变量和配置管理
5. CI/CD流程和自动化部署
6. 监控、日志和故障排除
"""

    def get_input_variables(self) -> List[str]:
        """获取输入变量列表"""
        return [
            "project_name",
            "project_path",
            "project_type",
            "programming_languages",
            "frameworks",
            "codebase_analysis",
            "deployment_configs",
            "cicd_directory",
            "custom_prompt"
        ]

    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return "你是一个专业的DevOps工程师和云架构师，擅长设计和实施高效可靠的部署方案。"

    def get_constraints(self) -> List[str]:
        """将写作要求迁移为约束条件"""
        return [
            "使用Markdown格式",
            "提供具体的部署命令和配置示例",
            "内容必须基于实际项目部署配置",
            "输出包含：环境要求、部署步骤、配置管理、CI/CD、监控运维",
            "考虑不同部署环境（开发、测试、生产）",
            "包含故障排除和最佳实践",
            "总长度控制在1000-1500字",
        ]

    def get_priority(self, context: ExecutionContext) -> int:
        """获取优先级 - 部署说明优先级较低"""
        return 60

    def get_dependencies(self) -> List[str]:
        """获取依赖 - 依赖安装和架构说明"""
        return ["installation", "architecture"]
