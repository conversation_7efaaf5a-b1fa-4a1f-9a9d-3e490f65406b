"""
安装说明生成描述器

通过提示词指导LLM生成项目安装和环境配置说明
"""

import logging
from typing import List

from ...execution_framework.base.step_type import StepType
from ...execution_framework.models.context import ExecutionContext
from ...execution_framework.models.tool_instruction import ToolInstruction
from .base_generation_descriptor import BaseGenerationDescriptor

logger = logging.getLogger(__name__)


class InstallationDescriptor(BaseGenerationDescriptor):
    """项目安装说明生成描述器 - 指导LLM生成安装配置指南"""

    def __init__(self):
        """初始化安装说明生成描述器"""
        super().__init__(StepType.INSTALLATION, "installation")

    def get_description(self) -> str:
        """获取描述器描述"""
        return "指导LLM生成项目安装、环境配置和依赖管理说明"

    def get_tool_instructions(self, context: ExecutionContext) -> List[ToolInstruction]:
        """获取工具调用指令列表 - 每个工具只定义一次"""
        return [
            ToolInstruction(
                name="codebase-retrieval",
                parameters={
                    "information_request": "项目依赖管理、环境要求、安装步骤、配置文件、构建脚本"
                },
                description="获取项目安装相关信息",
                result_key="codebase_analysis"
            ),
            ToolInstruction(
                name="view-file",
                parameters={"path": "package.json"},
                description="查看项目配置文件（可多次调用查看不同文件）",
                result_key="config_files"
            )
        ]

    def get_prompt_template(self) -> str:
        """获取提示词模板 - 指导LLM如何生成安装说明"""
        return """
## 项目信息
项目名称: {project_name}
项目路径: {project_path}
项目类型: {project_type}
编程语言: {programming_languages}
技术框架: {frameworks}

## 代码库分析
{codebase_analysis}

## 配置文件信息
{config_files}

## 自定义要求
{custom_prompt}

## 生成任务

请基于以上项目信息，使用可用工具分析项目配置后，生成详细的安装说明。

### 工具使用指导：
1. 使用 codebase-retrieval 获取项目依赖和安装相关信息
2. 使用 view-file 查看多个配置文件：
   - package.json（Node.js项目）
   - requirements.txt（Python项目）
   - Dockerfile（容器化配置）
   - pom.xml（Java Maven项目）
   - Cargo.toml（Rust项目）
   - go.mod（Go项目）

### 重点包含：
1. 系统环境要求（操作系统、运行时版本等）
2. 依赖安装步骤（包管理器、依赖包等）
3. 项目配置说明（环境变量、配置文件等）
4. 构建和启动步骤
5. 常见问题和解决方案
"""

    def get_input_variables(self) -> List[str]:
        """获取输入变量列表"""
        return [
            "project_name",
            "project_path",
            "project_type",
            "programming_languages",
            "frameworks",
            "codebase_analysis",
            "config_files",
            "custom_prompt"
        ]

    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return "你是一个专业的DevOps工程师和技术文档专家，擅长编写清晰易懂的安装配置指南。"

    def get_constraints(self) -> List[str]:
        """将写作要求迁移为约束条件"""
        return [
            "使用Markdown格式",
            "提供清晰的步骤编号和命令示例",
            "内容必须基于实际项目配置文件和依赖",
            "输出包含：环境要求、安装步骤、配置说明、启动命令、故障排除",
            "每个步骤提供具体的命令行示例",
            "考虑不同操作系统的差异",
            "总长度控制在800-1200字",
        ]

    def get_priority(self, context: ExecutionContext) -> int:
        """获取优先级 - 安装说明优先级中等"""
        return 80

    def get_dependencies(self) -> List[str]:
        """获取依赖 - 依赖功能特性部分"""
        return ["features"]
