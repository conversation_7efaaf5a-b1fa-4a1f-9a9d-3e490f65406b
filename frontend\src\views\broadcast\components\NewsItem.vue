<template>
  <div
    class="news-item"
    :style="`width: ${width};height: ${height};`"
    v-preventLongPress="() => handlePreventLongPress()">
    <div class="left" :class="{ mobile: device === 'mobile', fill: !imageUrl }">
      <div class="title">
        <img
          class="markicon"
          v-if="project.content_type === 'article'"
          src="@/assets/images/icon40.png"
          alt="" />
        <div class="text">{{ project[getPropKey.title] }}</div>
      </div>
      <div
        ref="tagsRef"
        class="card-tags"
        v-if="project[getPropKey.tags]?.length">
        <div
          class="tag-item"
          v-for="(tag, index) in project[getPropKey.tags]"
          v-show="isItemVisible(index)">
          {{ tag }}
        </div>
      </div>
      <div
        class="content"
        :style="{
          marginTop: project.content_type === 'article' ? '-1px' : '2px',
        }">
        {{ project[getPropKey.summary] }}
      </div>
      <div class="meta">
        <div class="meta-item">
          <img class="meta-item-icon" src="@/assets/images/icon26.png" alt="" />
          {{ formatStar(project[getPropKey.read_count]) || "--" }}
        </div>
        <div
          class="meta-item cursor"
          @click.stop="handleCollect"
          @mousedown.stop
          @mouseup.stop>
          <Button1
            gray
            class="meta-item-icon"
            :filled="project[getPropKey.is_collected]"></Button1>
          {{ formatStar(project[getPropKey.collect_count]) || "--" }}
        </div>
        <div class="meta-item" v-if="project.content_type === 'article'">
          <img class="meta-item-icon" src="@/assets/images/icon27.png" alt="" />
          {{ formatStar(project[getPropKey.comment_count]) || "--" }}
        </div>
        <div class="meta-item readed" v-if="isReaded">
          <img class="meta-item-icon" src="@/assets/images/icon35.png" alt="" />
        </div>
        <div class="meta-item time">
          {{ dayjs(project.created_at).format("YYYY-MM-DD") }}
        </div>
      </div>
    </div>
    <div v-if="imageUrl" class="right" :class="{ mobile: device === 'mobile' }">
      <img :src="imageUrl" alt="" />
    </div>
  </div>
</template>

<script setup>
import dayjs from "dayjs";
import { formatStar } from "@/utils";
import { addUserCollect, deleteUserCollect } from "@/api/broadcast.js";
import preventLongPress from "@/directive/common/preventLongPress";
import useUserStore from "@/store/modules/user";
import Button1 from "@/components/Button/index1.vue";
const { proxy } = getCurrentInstance();
const userStore = useUserStore();
import useAppStore from "@/store/modules/app";
const device = computed(() => useAppStore().device);
defineOptions({
  directives: {
    preventLongPress,
  },
});
const props = defineProps({
  project: {
    type: Object,
    default: () => ({}),
  },
  width: {
    type: [Number, String],
    default: "100%",
  },
  height: {
    type: [Number, String],
    default: "auto",
  },
  // 是否已读状态
  isReaded: {
    type: Boolean,
    default: false,
  },
});
const getPropKey = computed(() => {
  if (props.project.content_type === "project") {
    return {
      id: "id",
      title: "name",
      summary: "description_recommend",
      tags: "tags",
      read_count: "read_count",
      is_collected: "is_collected",
      collect_count: "collect_count",
      comment_count: "comment_count",
      cover_image: "image_url",
    };
  } else {
    return {
      id: "id",
      title: "title",
      summary: "summary",
      tags: "tags",
      read_count: "read_count",
      is_collected: "is_collected",
      collect_count: "collect_count",
      comment_count: "comment_count",
      cover_image: "cover_image",
    };
  }
});
const emit = defineEmits();
const handlePreventLongPress = () => {
  emit("toDetails", props.project);
};
// 图片预加载函数，设置超时时间
const preloadImage = (url, timeout = 5000) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const timer = setTimeout(() => {
      reject(new Error("Image load timeout"));
    }, timeout);

    img.onload = () => {
      clearTimeout(timer);
      resolve(url);
    };

    img.onerror = () => {
      clearTimeout(timer);
      reject(new Error("Image load failed"));
    };

    img.src = url;
  });
};
let imageUrl = ref("");
// 监听 cover_image 变化
watch(
  () => props.project[getPropKey.value.cover_image],
  async (newUrl) => {
    if (newUrl) {
      try {
        let url = await preloadImage(newUrl, 3000); // 3秒超时
        console.warn("url failed:", url);
        imageUrl.value = url;
      } catch (e) {
        console.warn("Image load failed:", e);
        imageUrl.value = "";
      }
    }
  },
  { immediate: true }
);
const handleCollect = async () => {
  // 判断用户是否登录，如果未登录，则提示用户去登录
  if (!userStore?.name) {
    proxy.$modal.msgWarning("登录后即可收藏");
    return;
  }
  let res = {};
  if (props.project[getPropKey.value.is_collected]) {
    let params = {};
    if (props.project.content_type === "article") {
      params.article_id = props.project[getPropKey.value.id];
    } else {
      params.project_id = props.project[getPropKey.value.id];
    }
    res = await deleteUserCollect(params);
    if (res.code === 200) {
      proxy.$modal.msgSuccess("已取消收藏");
      props.project.is_collected =
        !props.project[getPropKey.value.is_collected];
      props.project[getPropKey.value.collect_count]--;
      // 通知父元素收藏操作成功
      emit("collectSuccess", "delete");
    }
  } else {
    let params = {};
    if (props.project.content_type === "article") {
      params.article_id = props.project[getPropKey.value.id];
    } else {
      params.project_id = props.project[getPropKey.value.id];
    }
    res = await addUserCollect(params);
    if (res.code === 200) {
      proxy.$modal.msgSuccess("已收藏");
      props.project[getPropKey.value.is_collected] =
        !props.project[getPropKey.value.is_collected];
      props.project[getPropKey.value.collect_count]++;
      // 通知父元素收藏操作成功
      emit("collectSuccess", "add");
    }
  }
};
// ---------------动态计算tags显示个数 start-------------------
let tagsRef = ref(null);
const visibleCount = ref(0);
const isItemVisible = (index) => {
  return index < visibleCount.value;
};
let resizeObserver = null;

const updateVisibleItems = () => {
  if (!tagsRef.value) return;

  const container = tagsRef.value;
  const containerWidth = container.offsetWidth;
  const items = container.querySelectorAll(".tag-item");

  let totalWidth = 0;
  let count = 0;

  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    const itemWidth = item.offsetWidth;

    if (totalWidth + itemWidth + i * 4 <= containerWidth) {
      totalWidth += itemWidth;
      count++;
    } else {
      break;
    }
  }

  visibleCount.value = count;
};
onMounted(() => {
  updateVisibleItems();

  // 使用 ResizeObserver 监听容器大小变化
  resizeObserver = new ResizeObserver(() => {
    requestAnimationFrame(() => {
      updateVisibleItems();
    });
  });

  if (tagsRef.value) {
    resizeObserver.observe(tagsRef.value);
  }
});

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
});
// ---------------动态计算tags显示个数 end-------------------
</script>

<style scoped lang="scss">
.news-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: solid 1px #ddd;
  padding-bottom: 4px;
  cursor: pointer;
}
.left {
  width: calc(100% - 170px);
  &.mobile {
    width: calc(100% - 110px);
  }
  &.fill {
    width: 100%;
  }
}
.right {
  width: 140px;
  height: 96px;
  overflow: hidden;
  margin-left: 10px;
  border-radius: 4px;
  &.mobile {
    width: 100px;
    height: 68px;
  }
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: right;
    display: block;
    transition: transform 0.7s;
    &:hover {
      transform: scale(1.2);
    }
  }
}
.title {
  font-size: 16px;
  font-weight: bold;
  color: #525252;
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  .text {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: calc(100% - 30px);
  }
  .markicon {
    width: 18px;
    height: 18px;
  }
}
.card-tags {
  color: #a7a7a7;
  margin: 4px 0 10px;
  display: flex;
  align-items: center;
  font-size: 12px;
  overflow: hidden;
  .tag-item {
    display: inline-block;
    background-color: #f0f7f9;
    padding: 0px 2px;
    border-radius: 2px;
    height: 16px;
    display: flex;
    align-items: center;
    white-space: nowrap;
    & + .tag-item {
      margin-left: 4px;
    }
  }
}
.content {
  font-size: 14px;
  color: #898989;
  margin-top: -1px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  line-clamp: 2;
  -webkit-line-clamp: 2; /* 限制显示的行数 */
  overflow: hidden;
  text-overflow: ellipsis;
}
.meta {
  font-size: 12px;
  color: #898989;
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  .meta-item {
    display: flex;
    align-items: center;
    &.time {
      color: #8a8a8a;
      position: absolute;
      right: 0;
      bottom: 0;
    }
    &.readed {
      color: #19da32;
    }
    &.cursor {
      cursor: pointer;
    }
    .meta-item-icon {
      width: 16px;
      height: 16px;
      margin-right: 2px;
    }
  }
}

// .meta-favorite {
//   padding: 0 4px;
//   display: flex;
//   align-items: center;
//   justify-content: flex-end;
//   font-size: 12px;
//   color: #fff;
//   height: 100%;
//   width: fit-content;
//   .meta-favorite-icon {
//     width: 16px;
//     height: 16px;
//     margin-right: 4px;
//   }
// }
</style>
