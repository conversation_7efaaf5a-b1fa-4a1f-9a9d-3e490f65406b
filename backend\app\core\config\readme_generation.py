"""
README生成服务配置
"""

from pydantic import Field

from .base import BaseAppConfig


class ReadmeGenerationSettings(BaseAppConfig):
    """README生成服务配置"""
    
    # 存储配置 - 固定使用MongoDB
    # storage_type 已移除，系统固定使用MongoDB存储
    
    # 生成配置
    default_model_provider: str = Field(
        default="openai",
        description="默认模型提供商"
    )
    
    default_model_name: str = Field(
        default="gpt-3.5-turbo",
        description="默认模型名称"
    )
    
    # 缓存配置
    enable_cache: bool = Field(
        default=True,
        description="是否启用缓存"
    )
    
    cache_ttl: int = Field(
        default=3600,
        description="缓存过期时间（秒）"
    )
    
    # 并发配置
    max_concurrent_generations: int = Field(
        default=5,
        description="最大并发生成数量"
    )

    generation_timeout: int = Field(
        default=300,
        description="生成超时时间（秒）"
    )
