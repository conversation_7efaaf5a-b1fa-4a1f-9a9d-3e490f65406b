# @GeneratedBy:AI
"""
arXiv论文处理状态跟踪模型（完整版）
"""
from datetime import datetime
from sqlalchemy import Column, String, Text, DateTime, Boolean, Integer, Float, JSON
from sqlalchemy.orm import Mapped, mapped_column
from app.models.model_base import ModelBase


class ArxivProcessingStatusModel(ModelBase):
    """arXiv论文处理状态表"""
    __tablename__ = "arxiv_processing_status"

    # 文件信息
    file_name: Mapped[str] = mapped_column(String(512), comment='文件名')
    file_path: Mapped[str] = mapped_column(String(1024), comment='文件路径')
    file_size: Mapped[int] = mapped_column(Integer, comment='文件大小(字节)')
    file_hash: Mapped[str] = mapped_column(String(64), comment='文件MD5哈希')
    file_checksum: Mapped[str] = mapped_column(String(128), comment='文件校验和(SHA256)')

    # NAS信息
    nas_ip: Mapped[str] = mapped_column(String(45), comment='NAS设备IP地址')

    # 处理状态
    is_processed: Mapped[bool] = mapped_column(Boolean, default=False, comment='是否已处理')
    processing_status: Mapped[str] = mapped_column(String(32), default='pending', comment='处理状态')
    error_message: Mapped[str] = mapped_column(Text, nullable=True, comment='错误信息')
    error_type: Mapped[str] = mapped_column(String(64), nullable=True, comment='错误类型')

    # 处理统计
    papers_extracted: Mapped[int] = mapped_column(Integer, default=0, comment='提取的论文数量')
    vectors_created: Mapped[int] = mapped_column(Integer, default=0, comment='创建的向量数量')

    # 处理配置
    max_papers_limit: Mapped[int] = mapped_column(Integer, default=100, comment='最大处理论文数')
    embedding_model: Mapped[str] = mapped_column(String(64), comment='使用的嵌入模型')

    # 时间戳
    processed_at: Mapped[datetime] = mapped_column(DateTime, nullable=True, comment='处理完成时间')