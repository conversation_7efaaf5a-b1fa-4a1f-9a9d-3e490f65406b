"""
模型提供商管理器
"""

import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Type
import structlog
import yaml

from .base import BaseModelProvider
from .models import (
    ModelProviderConfig, ModelConfig, ModelProviderType,
    ModelProviderStatus, ModelListResponse, ModelSwitchRequest,
    ProviderModels
)
from .providers import (
    OpenAIProvider, GoogleProvider, OpenRouterProvider, OllamaProvider,
    BaiduProvider, AlibabaProvider, ZhipuProvider, MoonshotProvider, DeepSeekProvider
)

logger = structlog.get_logger(__name__)


class ModelProviderManager:
    """模型提供商管理器"""
    
    def __init__(self):
        """初始化管理器"""
        self.providers: Dict[str, BaseModelProvider] = {}
        self.configs: Dict[str, ModelProviderConfig] = {}
        self.default_provider: Optional[str] = None
        self.current_model: Optional[str] = None

        # 注册提供商类型
        self.provider_classes: Dict[ModelProviderType, Type[BaseModelProvider]] = {
            ModelProviderType.OPENAI: OpenAIProvider,
            ModelProviderType.GOOGLE: GoogleProvider,
            ModelProviderType.OPENROUTER: OpenRouterProvider,
            ModelProviderType.OLLAMA: OllamaProvider,
            # 国内提供商
            ModelProviderType.BAIDU: BaiduProvider,
            ModelProviderType.ALIBABA: AlibabaProvider,
            ModelProviderType.ZHIPU: ZhipuProvider,
            ModelProviderType.MOONSHOT: MoonshotProvider,
            ModelProviderType.DEEPSEEK: DeepSeekProvider,
        }

        # 管理器状态
        self.is_initialized = False
    
    def register_provider_class(self, provider_type: ModelProviderType, provider_class: Type[BaseModelProvider]):
        """
        注册提供商类
        
        Args:
            provider_type: 提供商类型
            provider_class: 提供商类
        """
        self.provider_classes[provider_type] = provider_class
        logger.info(f"注册模型提供商类: {provider_type}")
    
    async def initialize(self, config_path: Optional[str] = None) -> bool:
        """
        初始化管理器
        
        Args:
            config_path: 配置文件路径，默认使用config文件夹中的配置
            
        Returns:
            bool: 是否初始化成功
        """
        try:
            # 确定配置文件路径
            if config_path is None:
                current_dir = Path(__file__).parent
                config_path = current_dir / "config" / "model_providers_config.yml"
            
            # 加载配置
            await self.load_config(str(config_path))
            
            # 初始化所有提供商
            success_count = 0
            for name, config in self.configs.items():
                if await self.create_provider(name, config):
                    success_count += 1
            
            self.is_initialized = True
            logger.info(f"模型提供商管理器初始化完成，成功加载 {success_count}/{len(self.configs)} 个提供商")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"模型提供商管理器初始化失败: {e}")
            return False
    
    async def load_config(self, config_path: str) -> None:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
        """
        try:
            config_file = Path(config_path)
            if not config_file.exists():
                logger.warning(f"配置文件不存在: {config_path}")
                return
            
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            # 设置默认提供商
            self.default_provider = config_data.get('default_provider')
            self.current_model = config_data.get('current_model')
            
            # 加载提供商配置
            providers_config = config_data.get('providers', {})
            for name, provider_data in providers_config.items():
                try:
                    # 先构造提供商配置对象
                    config = ModelProviderConfig(**provider_data)

                    # 遍历模型配置，自动同步提供商级别的配置
                    for model in config.models:
                        # 如果模型配置中没有设置这些字段，则从提供商配置中同步
                        if model.api_key is None and config.api_key is not None:
                            model.api_key = config.api_key
                            logger.debug(f"模型 {model.name} 自动同步 api_key")

                        if model.secret_key is None and config.secret_key is not None:
                            model.secret_key = config.secret_key
                            logger.debug(f"模型 {model.name} 自动同步 secret_key")

                        if model.api_base is None and config.api_base is not None:
                            model.api_base = config.api_base
                            logger.debug(f"模型 {model.name} 自动同步 api_base")

                        if model.proxy is None and config.proxy is not None:
                            model.proxy = config.proxy
                            logger.debug(f"模型 {model.name} 自动同步 proxy")

                    self.configs[name] = config
                    logger.info(f"加载提供商配置: {name}")
                except Exception as e:
                    logger.error(f"加载提供商配置 {name} 失败: {e}")
            
            logger.info(f"配置加载完成，共 {len(self.configs)} 个提供商")
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
    
    async def create_provider(self, name: str, config: ModelProviderConfig) -> bool:
        """
        创建模型提供商
        
        Args:
            name: 提供商名称
            config: 提供商配置
            
        Returns:
            bool: 是否创建成功
        """
        try:
            # 获取提供商类
            provider_class = self.provider_classes.get(config.provider)
            if not provider_class:
                logger.error(f"不支持的模型提供商类型: {config.provider}")
                return False
            
            # 创建提供商实例
            provider = provider_class(config)
            
            # 初始化提供商
            if await provider.initialize():
                self.providers[name] = provider
                logger.info(f"模型提供商 {name} 创建成功")
                return True
            else:
                logger.error(f"模型提供商 {name} 初始化失败")
                return False
                
        except Exception as e:
            logger.error(f"创建模型提供商 {name} 失败: {e}")
            return False
    
    def get_provider(self, name: Optional[str] = None) -> Optional[BaseModelProvider]:
        """
        获取模型提供商
        
        Args:
            name: 提供商名称，为None时返回默认提供商
            
        Returns:
            Optional[BaseModelProvider]: 提供商实例
        """
        if name is None:
            name = self.default_provider
        
        if name and name in self.providers:
            return self.providers[name]
        
        # 如果指定的提供商不存在，返回第一个可用的
        if self.providers:
            return next(iter(self.providers.values()))
        
        return None
    
    async def get_model(self, model_name: str, provider_name: Optional[str] = None) -> Optional[ModelConfig]:
        """
        获取模型配置
        
        Args:
            model_name: 模型名称
            provider_name: 提供商名称
            
        Returns:
            Optional[ModelConfig]: 模型配置
        """
        if provider_name:
            provider = self.get_provider(provider_name)
            if provider:
                return await provider.get_model(model_name)
        else:
            # 在所有提供商中搜索
            for provider in self.providers.values():
                model = await provider.get_model(model_name)
                if model:
                    return model
        
        return None
    
    async def list_models(self, provider_name: Optional[str] = None) -> ModelListResponse:
        """
        列出所有模型

        Args:
            provider_name: 提供商名称，为None时列出所有提供商的模型

        Returns:
            ModelListResponse: 模型列表响应
        """
        provider_models_list = []
        total_models = 0
        current_provider = None

        if provider_name:
            provider = self.get_provider(provider_name)
            if provider:
                models = await provider.get_models()
                status = provider.get_status()
                provider_models_list.append(ProviderModels(
                    provider_name=provider_name,
                    provider_type=provider.provider_type,
                    is_available=status.is_available,
                    models=models,
                    model_count=len(models)
                ))
                total_models += len(models)
        else:
            for name, provider in self.providers.items():
                models = await provider.get_models()
                status = provider.get_status()
                provider_models_list.append(ProviderModels(
                    provider_name=name,
                    provider_type=provider.provider_type,
                    is_available=status.is_available,
                    models=models,
                    model_count=len(models)
                ))
                total_models += len(models)

        # 确定当前提供商
        if self.current_model:
            for provider_models in provider_models_list:
                for model in provider_models.models:
                    if model.name == self.current_model:
                        current_provider = provider_models.provider_name
                        break
                if current_provider:
                    break

        return ModelListResponse(
            providers=provider_models_list,
            current_model=self.current_model,
            current_provider=current_provider,
            total_providers=len(provider_models_list),
            total_models=total_models
        )
    
    async def switch_model(self, request: ModelSwitchRequest) -> bool:
        """
        切换当前模型
        
        Args:
            request: 切换请求
            
        Returns:
            bool: 是否切换成功
        """
        try:
            # 验证模型是否存在
            model = await self.get_model(request.model_name, request.provider)
            if not model:
                logger.error(f"模型不存在: {request.model_name}")
                return False
            
            # 验证模型是否可用
            provider = self.get_provider(request.provider)
            if provider and not await provider.validate_model(request.model_name):
                logger.error(f"模型不可用: {request.model_name}")
                return False
            
            # 切换模型
            old_model = self.current_model
            self.current_model = request.model_name
            
            logger.info(f"模型切换成功: {old_model} -> {request.model_name}, 原因: {request.reason}")
            return True
            
        except Exception as e:
            logger.error(f"切换模型失败: {e}")
            return False
    
    def list_providers(self) -> List[str]:
        """
        列出所有提供商名称
        
        Returns:
            List[str]: 提供商名称列表
        """
        return list(self.providers.keys())
    
    def get_status(self, provider_name: Optional[str] = None) -> Optional[ModelProviderStatus]:
        """
        获取提供商状态
        
        Args:
            provider_name: 提供商名称
            
        Returns:
            Optional[ModelProviderStatus]: 状态信息
        """
        provider = self.get_provider(provider_name)
        return provider.get_status() if provider else None
    
    async def health_check(self, provider_name: Optional[str] = None) -> Dict[str, bool]:
        """
        健康检查
        
        Args:
            provider_name: 提供商名称，为None时检查所有提供商
            
        Returns:
            Dict[str, bool]: 健康状态字典
        """
        results = {}
        
        if provider_name:
            provider = self.get_provider(provider_name)
            if provider:
                results[provider_name] = await provider.health_check()
        else:
            for name, provider in self.providers.items():
                results[name] = await provider.health_check()
        
        return results
    
    async def shutdown(self) -> None:
        """关闭管理器"""
        try:
            # 关闭所有提供商
            for provider in self.providers.values():
                await provider.shutdown()
            
            self.providers.clear()
            self.is_initialized = False
            logger.info("模型提供商管理器已关闭")
            
        except Exception as e:
            logger.error(f"关闭模型提供商管理器失败: {e}")
