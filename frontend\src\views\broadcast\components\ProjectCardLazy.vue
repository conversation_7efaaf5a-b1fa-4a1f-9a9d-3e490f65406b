<template>
  <div
    class="card"
    :class="{ 'is-pc': device === 'pc' }"
    :style="`background-color: ${
      randomBgColor
    };--backgroundColor: ${randomBgColor};width: ${width};height: ${height};`"
    v-preventLongPress="() => handlePreventLongPress()">
    <img
      v-if="isReaded"
      class="markicon"
      src="@/assets/images/icon35.png"
      alt="" />
    <div class="main-cover-wrap">
      <LazyImg
        v-if="coverImage"
        class="main-cover main-cover0"
        :url="coverImage"
        @load="imageLoad"
        @error="imageError"
        @success="imageSuccess"></LazyImg>
      <div v-else class="main-cover main-cover1">
        <div class="text">{{ project[getPropKey.summary] }}</div>
      </div>
    </div>
    <div class="card-name" :title="project[getPropKey.title]">
      <img
        v-if="project.content_type === 'article'"
        class="nameicon"
        src="@/assets/images/icon40.png"
        alt="" />
      <div class="nametext">{{ project[getPropKey.title] }}</div>
    </div>
    <div
      ref="tagsRef"
      class="card-tags"
      v-if="project[getPropKey.tags]?.length">
      <div
        class="tag-item"
        v-for="(tag, index) in project[getPropKey.tags]"
        v-show="isItemVisible(index)">
        {{ tag }}
      </div>
    </div>
    <p class="card-text" :title="project[getPropKey.summary]">
      {{ project[getPropKey.summary] }}
    </p>
    <div class="card-meta">
      <div
        class="meta-item"
        @click.stop="handleCollect"
        @mousedown.stop
        @mouseup.stop>
        <Button1
          gray
          class="meta-item-icon"
          :filled="project[getPropKey.is_collected]"></Button1>
        {{ formatStar(project[getPropKey.collect_count]) || "--" }}
      </div>
      <div class="meta-item">
        <img class="meta-item-icon" src="@/assets/images/icon26.png" alt="" />
        {{ formatStar(project[getPropKey.read_count]) || "--" }}
      </div>
      <div class="meta-item" v-if="project.content_type === 'article'">
        <img class="meta-item-icon" src="@/assets/images/icon27.png" alt="" />
        {{ formatStar(project[getPropKey.comment_count]) || "--" }}
      </div>
    </div>
    <div class="time" v-if="time?.length">
      {{ dayjs(time).format("YYYY-MM-DD HH:mm") }}
    </div>
  </div>
</template>

<script setup>
import { formatStar } from "@/utils";
import { addUserCollect, deleteUserCollect } from "@/api/broadcast.js";
import preventLongPress from "@/directive/common/preventLongPress";
import useUserStore from "@/store/modules/user";
import { LazyImg } from "vue-waterfall-plugin-next";
import Button1 from "@/components/Button/index1.vue";
import dayjs from "dayjs";
import { watch } from "vue";
import useAppStore from "@/store/modules/app";
const device = computed(() => useAppStore().device);
const { proxy } = getCurrentInstance();
const userStore = useUserStore();
defineOptions({
  directives: {
    preventLongPress,
  },
});
const props = defineProps({
  project: {
    type: Object,
    default: () => ({}),
  },
  width: {
    type: [Number, String],
    default: "100%",
  },
  height: {
    type: [Number, String],
    default: "auto",
  },
  time: {
    type: [Number, String],
    default: "",
  },
  // 是否已读状态
  isReaded: {
    type: Boolean,
    default: false,
  },
});
const getPropKey = computed(() => {
  if (props.project.content_type === "project") {
    return {
      id: "id",
      title: "name",
      summary: "description_recommend",
      tags: "tags",
      read_count: "read_count",
      is_collected: "is_collected",
      collect_count: "collect_count",
      comment_count: "comment_count",
      cover_image: "image_url",
      background_color: "background_color",
      button_color: "button_color",
    };
  } else {
    return {
      id: "id",
      title: "title",
      summary: "summary",
      tags: "tags",
      read_count: "read_count",
      is_collected: "is_collected",
      collect_count: "collect_count",
      comment_count: "comment_count",
      cover_image: "cover_image",
      background_color: "background_color",
      button_color: "button_color",
    };
  }
});
// 定义卡片可选的背景色
const availableBgColors = ref(["#E3ECF0", "#E3E5F5", "#F7F4EB", "#faeef8"]);
// 从可选的背景色中随机选一个
const randomBgColor = computed(() => {
  return availableBgColors.value[
    Math.floor(Math.random() * availableBgColors.value.length)
  ];
});
let coverImage = ref("");
watch(
  () => props.project[getPropKey.value.cover_image],
  (newUrl) => {
    coverImage.value = newUrl;
  },
  { immediate: true }
);
const emit = defineEmits(["toDetails", "collectSuccess"]);
const handlePreventLongPress = () => {
  emit("toDetails", props.project);
};
const handleCollect = async () => {
  // 判断用户是否登录，如果为登录，则提示用户去登录
  if (!userStore?.name) {
    proxy.$modal.msgWarning("登录后即可收藏");
    return;
  }
  let res = {};
  if (props.project[getPropKey.value.is_collected]) {
    let params = {};
    if (props.project.content_type === "article") {
      params.article_id = props.project[getPropKey.value.id];
    } else {
      params.project_id = props.project[getPropKey.value.id];
    }
    res = await deleteUserCollect(params);
    if (res.code === 200) {
      proxy.$modal.msgSuccess("已取消收藏");
      props.project[getPropKey.value.is_collected] =
        !props.project[getPropKey.value.is_collected];
      props.project[getPropKey.value.collect_count]--;
      // 通知父元素收藏操作成功
      emit("collectSuccess", "delete");
    }
  } else {
    let params = {};
    if (props.project.content_type === "article") {
      params.article_id = props.project[getPropKey.value.id];
    } else {
      params.project_id = props.project[getPropKey.value.id];
    }
    res = await addUserCollect(params);
    if (res.code === 200) {
      proxy.$modal.msgSuccess("已收藏");
      props.project[getPropKey.value.is_collected] =
        !props.project[getPropKey.value.is_collected];
      props.project[getPropKey.value.collect_count]++;
      // 通知父元素收藏操作成功
      emit("collectSuccess", "add");
    }
  }
  // 保存收藏变更的数据，方便回到首页时同步
  let pathname = window.location.pathname;
  if (pathname === "/broadcast/index") {
    return;
  }
  let collectChangeData = sessionStorage.getItem("collectChangeData");
  if (collectChangeData?.length > 0) {
    collectChangeData = JSON.parse(collectChangeData);
    // 判断是否已经存在
    let existIndex = -1;
    for (let i = 0, l = collectChangeData.length; i < l; i++) {
      if (
        collectChangeData[i][props.project[getPropKey.value.id]] ===
        props.project[getPropKey.value.id]
      ) {
        existIndex = i;
        break;
      }
    }
    if (existIndex > -1) {
      collectChangeData[existIndex] = {
        [props.project[getPropKey.value.id]]: [
          props.project[getPropKey.value.is_collected],
          props.project[getPropKey.value.collect_count],
        ],
      };
    } else {
      collectChangeData.push({
        [props.project[getPropKey.value.id]]: [
          props.project[getPropKey.value.is_collected],
          props.project[getPropKey.value.collect_count],
        ],
      });
    }

    sessionStorage.setItem(
      "collectChangeData",
      JSON.stringify(collectChangeData)
    );
  } else {
    sessionStorage.setItem(
      "collectChangeData",
      JSON.stringify([
        {
          [props.project[getPropKey.value.id]]: [
            props.project[getPropKey.value.is_collected],
            props.project[getPropKey.value.collect_count],
          ],
        },
      ])
    );
  }
};
function imageLoad(url) {
  // console.log(`${url}: 加载完成`)
}

function imageError(url) {
  // console.error(`${url}: 加载失败`);
  coverImage.value = "";
}

function imageSuccess(url) {
  // console.log(`${url}: 加载成功`)
}

// ---------------动态计算tags显示个数 start-------------------
let tagsRef = ref(null);
const visibleCount = ref(0);
const isItemVisible = (index) => {
  return index < visibleCount.value;
};
let resizeObserver = null;

const updateVisibleItems = () => {
  if (!tagsRef.value) return;

  const container = tagsRef.value;
  const containerWidth = container.offsetWidth;
  const items = container.querySelectorAll(".tag-item");

  let totalWidth = 0;
  let count = 0;

  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    const itemWidth = item.offsetWidth;

    if (totalWidth + itemWidth + i * 4 <= containerWidth) {
      totalWidth += itemWidth;
      count++;
    } else {
      break;
    }
  }

  visibleCount.value = count;
};
onMounted(() => {
  updateVisibleItems();

  // 使用 ResizeObserver 监听容器大小变化
  resizeObserver = new ResizeObserver(() => {
    requestAnimationFrame(() => {
      updateVisibleItems();
    });
  });

  if (tagsRef.value) {
    resizeObserver.observe(tagsRef.value);
  }
});

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
});
// ---------------动态计算tags显示个数 end-------------------
</script>

<style scoped lang="scss">
.card {
  cursor: pointer;
  padding: 10px 10px 0px 10px;
  border-radius: 16px;
  border-bottom: solid 0px #000;
  position: relative;
  margin-top: 10px;
  transition: all 0.3s;
  &.is-pc:hover {
    filter: drop-shadow(0 0 4px #bbb);
    transform: scale(1.05);
  }
  &::before {
    content: "";
    border-radius: 16px;
    position: absolute;
    top: -10px;
    left: 0;
    width: 100%;
    height: 60px;
    z-index: -1;
    background-color: var(--backgroundColor);
    // background-color: #ffe6e6;
    transform: skewY(2deg);
    transform-origin: bottom left;
  }
  &::after {
    content: "";
    border-radius: 16px;
    position: absolute;
    bottom: -14px;
    left: 0;
    width: 100%;
    height: 60px;
    z-index: -1;
    background-color: var(--backgroundColor);
    // background-color: #faeef8;
    transform: skewY(4deg);
    transform-origin: bottom left;
  }
  .markicon {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 24px;
    height: 24px;
    z-index: 1;
  }
  .main-cover-wrap {
    width: 100%;
    overflow: hidden;
    // border: solid 4px #fff;
    position: relative;
    .el-image {
      transition: transform 0.5s;
      &:hover {
        transform: scale(1.2);
      }
    }
  }
  .card-meta {
    display: flex;
    gap: 20px;
    align-items: center;
    margin: 20px 0 0;
    color: #898989;
    .meta-item {
      display: flex;
      align-items: center;
      font-size: 12px;
      &.readed {
        color: #19da32;
      }
      .meta-item-icon {
        width: 16px;
        height: 16px;
        margin-right: 2px;
      }
    }
  }
  .card-name {
    font-size: 14px;
    font-weight: bold;
    color: #525252;
    display: flex;
    align-items: center;
    .nameicon {
      width: 18px;
      height: 18px;
      display: block;
    }
    .nametext {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .card-tags {
    color: #a7a7a7;
    margin: 4px 0 10px;
    display: flex;
    align-items: center;
    font-size: 12px;
    overflow: hidden;
    .tag-item {
      display: inline-block;
      background-color: #f0f7f9;
      padding: 0px 2px;
      border-radius: 2px;
      height: 16px;
      display: flex;
      align-items: center;
      white-space: nowrap;
      & + .tag-item {
        margin-left: 4px;
      }
    }
  }
  .main-cover {
    width: 100%;
    display: block;
    transition: transform 0.5s;
    &:hover {
      transform: scale(1.2);
    }
    &.main-cover0 {
      margin-bottom: 8px;
    }
    &.main-cover1 {
      // background-color: #f3efee;
      height: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
      .text {
        font-size: 18px;
        width: 80%;
        max-height: 70%;
        text-align: center;
        color: #525252;
        line-height: 22px;

        display: -webkit-box;
        -webkit-box-orient: vertical;
        line-clamp: 3;
        -webkit-line-clamp: 3; /* 限制显示的行数 */
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  .card-text {
    font-size: 14px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    line-clamp: 6;
    -webkit-line-clamp: 6; /* 限制显示的行数 */
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 10px 0px;
    color: #898989;
    text-align: justify;
  }
  .time {
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 10px 0;
    color: #656464;
  }
  .arrow-wrap {
    width: 30px;
    height: 30px;
    background-color: aqua;
    border-radius: 50%;
    position: absolute;
    left: 50%;
    bottom: -72px;
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(0.6);
    .arrow {
      width: 20px;
      height: 20px;
      display: block;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translateX(-50%), translateY(-50%);
    }
  }
}
@media screen and (max-width: 768px) {
  .card {
    .card-meta {
      .meta-name {
        font-weight: normal;
      }
    }
  }
}
:deep(.lazy__img[lazy="error"]) {
  width: 100% !important;
}
// 定义rotate
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
:deep(.lazy__img[lazy="loading"]) {
  width: 40% !important;
  // 让这个图片元素一直旋转
  animation: rotate 3s linear infinite;
}
</style>
