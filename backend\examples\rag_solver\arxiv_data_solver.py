import os
import hashlib
import logging
from datetime import datetime
from typing import List, Dict, Optional
from pathlib import Path

from smb.SMBConnection import SMBConnection
from smb.smb_structs import OperationFailure
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

# 导入项目相关模块
from app.core.di.containers import Container
from app.models.rag.arxiv import ArxivProcessingStatusModel
from app.utils.rag_utils.nas_arxiv_utils import NASArxivUtil

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ArxivDataSolver:
    """arXiv数据解析器类"""

    def __init__(self, container: Container = None):
        """
        初始化arXiv数据解析器

        Args:
            container: 依赖注入容器，如果为None则创建新实例
        """
        self.container = container or Container()
        self.smb_conn = None
        self.nas_util = NASArxivUtil()

    def connect_to_nas(self, username: str, password: str, client_name: str,
                       server_name: str, server_ip: str, port: int = 445) -> bool:
        """
        连接到NAS服务器

        Args:
            username: 用户名
            password: 密码
            client_name: 客户端名称
            server_name: 服务器名称
            server_ip: 服务器IP地址
            port: 端口号，默认445

        Returns:
            bool: 连接是否成功
        """
        try:
            # 创建SMB连接对象
            self.smb_conn = SMBConnection(
                username,
                password,
                client_name,
                server_name,
                use_ntlm_v2=True
            )

            # 连接到服务器
            success = self.smb_conn.connect(server_ip, port)
            if success:
                logger.info(f"成功连接到NAS服务器 {server_ip}:{port}")
                return True
            else:
                logger.error("连接NAS服务器失败")
                return False

        except Exception as e:
            logger.error(f"连接NAS服务器时发生错误: {e}")
            return False

    def list_arxiv_files(self, share_name: str, remote_path: str) -> List[Dict]:
        """
        列出NAS上的arXiv文件

        Args:
            share_name: 共享文件夹名称
            remote_path: 远程路径

        Returns:
            list: 文件信息列表
        """
        try:
            if not self.smb_conn:
                logger.error("请先连接到NAS服务器")
                return []

            # 列出文件
            files = self.smb_conn.listPath(share_name, remote_path)
            file_list = []

            for file in files:
                # 只处理tar.gz文件
                if file.filename.endswith('.tar') and not file.isDirectory:
                    file_info = {
                        'name': file.filename,
                        'path': f"{remote_path.rstrip('/')}/{file.filename}",
                        'size': file.file_size,
                        'create_time': file.create_time,
                        'modify_time': file.last_write_time,
                        'is_directory': file.isDirectory
                    }
                    file_list.append(file_info)
                    logger.info(f"发现arXiv文件: {file.filename} (大小: {file.file_size} 字节)")

            logger.info(f"共找到 {len(file_list)} 个arXiv文件")
            return file_list

        except OperationFailure as e:
            logger.error(f"列出文件时发生错误: {e}")
            return []
        except Exception as e:
            logger.error(f"列出文件时发生未知错误: {e}")
            return []

    def calculate_file_hash(self, file_path: str) -> str:
        """
        计算文件MD5哈希值

        Args:
            file_path: 文件路径

        Returns:
            str: MD5哈希值
        """
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希时发生错误: {e}")
            return ""

    def save_file_to_database(self, file_info: Dict, nas_ip: str, nas_device_id: str) -> bool:
        """
        将文件信息保存到数据库

        Args:
            file_info: 文件信息字典
            nas_ip: NAS设备IP地址
            nas_device_id: NAS设备ID

        Returns:
            bool: 保存是否成功
        """
        try:
            # 获取同步数据库会话提供者
            session_provider = self.container.session_provider()

            # 使用正确的数据库访问方式
            with session_provider() as session:
                # 检查文件是否已存在
                existing_file = session.query(ArxivProcessingStatusModel).filter(
                    ArxivProcessingStatusModel.file_name == file_info['name']
                ).first()

                if existing_file:
                    logger.info(f"文件 {file_info['name']} 已存在于数据库中，跳过")
                    return True

                # 创建新的文件记录
                file_record = ArxivProcessingStatusModel(
                    # 文件信息
                    file_name=file_info['name'],
                    file_path=file_info['path'],
                    file_size=file_info['size'],
                    file_hash="",  # 需要下载后才能计算
                    file_checksum="",  # 需要下载后才能计算

                    # NAS信息
                    nas_ip=nas_ip,

                    # 处理状态
                    is_processed=False,
                    processing_status='pending',
                    error_message=None,
                    error_type=None,
                    # 处理统计
                    papers_extracted=0,
                    vectors_created=0,
                    # 处理配置
                    max_papers_limit=100,
                    embedding_model='default',
                    # 时间戳
                    processed_at=None
                )

                # 保存到数据库
                session.add(file_record)
                session.commit()

                logger.info(f"成功保存文件信息到数据库: {file_info['name']}")
                return True

        except SQLAlchemyError as e:
            logger.error(f"保存文件信息到数据库时发生错误: {e}")
            return False
        except Exception as e:
            logger.error(f"保存文件信息时发生未知错误: {e}")
            return False

    def process_arxiv_files(self, share_name: str, remote_path: str,
                            nas_ip: str, nas_device_id: str,
                            max_files: int = None) -> int:
        """
        处理arXiv文件列表并保存到数据库

        Args:
            share_name: 共享文件夹名称
            remote_path: 远程路径
            nas_ip: NAS设备IP地址
            nas_device_id: NAS设备ID
            max_files: 最大处理文件数量，None表示处理所有文件

        Returns:
            int: 成功处理的文件数量
        """
        try:
            # 获取文件列表
            files = self.list_arxiv_files(share_name, remote_path)

            if not files:
                logger.warning("没有找到任何arXiv文件")
                return 0

            # 限制处理数量
            if max_files:
                files = files[:max_files]

            logger.info(f"开始处理 {len(files)} 个arXiv文件")

            success_count = 0
            for file_info in files:
                if self.save_file_to_database(file_info, nas_ip, nas_device_id):
                    success_count += 1

            logger.info(f"处理完成，成功保存 {success_count}/{len(files)} 个文件到数据库")
            return success_count

        except Exception as e:
            logger.error(f"处理arXiv文件时发生错误: {e}")
            return 0

    def get_processing_status(self) -> Dict:
        """
        获取处理状态统计

        Returns:
            dict: 处理状态统计信息
        """
        try:
            # 获取同步数据库会话提供者
            session_provider = self.container.session_provider()

            with session_provider() as session:
                # 统计总数
                total_files = session.query(ArxivProcessingStatusModel).count()

                # 统计已处理
                processed_files = session.query(ArxivProcessingStatusModel).filter(
                    ArxivProcessingStatusModel.is_processed == True
                ).count()

                # 统计待处理
                pending_files = session.query(ArxivProcessingStatusModel).filter(
                    ArxivProcessingStatusModel.processing_status == 'pending'
                ).count()

                # 统计错误
                error_files = session.query(ArxivProcessingStatusModel).filter(
                    ArxivProcessingStatusModel.processing_status == 'error'
                ).count()

                return {
                    'total_files': total_files,
                    'processed_files': processed_files,
                    'pending_files': pending_files,
                    'error_files': error_files,
                    'processing_rate': round(processed_files / total_files * 100, 2) if total_files > 0 else 0
                }

        except Exception as e:
            logger.error(f"获取处理状态时发生错误: {e}")
            return {}

    def disconnect(self):
        """断开NAS连接"""
        if self.smb_conn:
            self.smb_conn.close()
            logger.info("已断开NAS连接")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()


def main():
    """主函数 - 演示如何使用arXiv数据解析器"""

    # NAS连接参数
    nas_config = {
        'username': "wzc",
        'password': "Helloshiyu123.",
        'client_name': "python_client",
        'server_name': "NAS",
        'server_ip': "***********",
        'port': 445
    }

    # 共享文件夹和路径
    share_name = "NetBackup"
    remote_path = "/arxiv/arxiv/files/src"
    nas_ip = "***********"
    nas_device_id = "NAS_001"

    # 创建解析器实例
    with ArxivDataSolver() as solver:
        try:
            # 连接到NAS
            if not solver.connect_to_nas(**nas_config):
                logger.error("无法连接到NAS服务器")
                return

            # 处理arXiv文件（限制处理前10个文件作为示例）
            success_count = solver.process_arxiv_files(
                share_name=share_name,
                remote_path=remote_path,
                nas_ip=nas_ip,
                nas_device_id=nas_device_id,
                max_files=100000  # 限制处理数量
            )

            logger.info(f"处理完成！成功保存了 {success_count} 个文件到数据库")

            # 显示处理状态
            status = solver.get_processing_status()
            logger.info(f"处理状态统计: {status}")

        except Exception as e:
            logger.error(f"程序执行时发生错误: {e}")


if __name__ == "__main__":
    main()