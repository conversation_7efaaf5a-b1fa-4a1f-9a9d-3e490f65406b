"""
执行流程编排器

实现执行流程的编排逻辑，支持步骤依赖、并行执行、错误处理等
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Set, Tuple
from enum import Enum
from datetime import datetime

from pydantic import BaseModel, Field

from ..base.descriptor import BaseDescriptor
from ..models.context import ExecutionContext
from ..models.execution_config import ExecutionConfig
from ..tools.tool_manager import ToolManager
from .descriptor_executor import DescriptorExecutor

logger = logging.getLogger(__name__)


class ExecutionStatus(str, Enum):
    """执行状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    CANCELLED = "cancelled"


class ExecutionStep(BaseModel):
    """执行步骤"""
    name: str = Field(..., description="步骤名称")
    descriptor: BaseDescriptor = Field(..., description="描述器")
    dependencies: List[str] = Field(default_factory=list)
    status: ExecutionStatus = Field(default=ExecutionStatus.PENDING)
    result: Optional[Any] = Field(default=None)
    error: Optional[str] = Field(default=None)
    start_time: Optional[datetime] = Field(default=None)
    end_time: Optional[datetime] = Field(default=None)

    model_config = {"arbitrary_types_allowed": True}

    @property
    def duration(self) -> Optional[float]:
        """获取执行时长（秒）"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None


class WorkflowOrchestrator:
    """工作流编排器"""
    
    def __init__(
        self,
        tool_manager: Optional[ToolManager] = None,
        descriptor_executor: Optional[DescriptorExecutor] = None
    ):
        self.tool_manager = tool_manager or ToolManager()
        self.descriptor_executor = descriptor_executor or DescriptorExecutor(
            tool_manager=self.tool_manager
        )
        self.steps: Dict[str, ExecutionStep] = {}
        self.execution_order: List[str] = []
        self._lock = asyncio.Lock()
    
    def add_step(
        self, 
        name: str, 
        descriptor: BaseDescriptor, 
        dependencies: Optional[List[str]] = None
    ):
        """
        添加执行步骤
        
        Args:
            name: 步骤名称
            descriptor: 描述器
            dependencies: 依赖的步骤列表
        """
        dependencies = dependencies or []
        
        # 验证依赖是否存在
        for dep in dependencies:
            if dep not in self.steps:
                logger.warning(f"步骤 {name} 的依赖 {dep} 不存在")
        
        self.steps[name] = ExecutionStep(
            name=name,
            descriptor=descriptor,
            dependencies=dependencies
        )
        
        logger.info(f"添加执行步骤: {name} (依赖: {dependencies})")
    
    def remove_step(self, name: str):
        """
        移除执行步骤
        
        Args:
            name: 步骤名称
        """
        if name in self.steps:
            # 检查是否有其他步骤依赖此步骤
            dependents = [
                step_name for step_name, step in self.steps.items()
                if name in step.dependencies
            ]
            
            if dependents:
                logger.warning(f"步骤 {name} 被以下步骤依赖: {dependents}")
            
            del self.steps[name]
            logger.info(f"移除执行步骤: {name}")
    
    def _build_execution_order(self) -> List[str]:
        """
        构建执行顺序（拓扑排序）
        
        Returns:
            List[str]: 执行顺序
        """
        # 使用Kahn算法进行拓扑排序
        in_degree = {name: 0 for name in self.steps}
        graph = {name: [] for name in self.steps}
        
        # 构建图和入度
        for name, step in self.steps.items():
            for dep in step.dependencies:
                if dep in self.steps:
                    graph[dep].append(name)
                    in_degree[name] += 1
        
        # 拓扑排序
        queue = [name for name, degree in in_degree.items() if degree == 0]
        result = []
        
        while queue:
            current = queue.pop(0)
            result.append(current)
            
            for neighbor in graph[current]:
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    queue.append(neighbor)
        
        # 检查是否有循环依赖
        if len(result) != len(self.steps):
            remaining = [name for name in self.steps if name not in result]
            raise ValueError(f"检测到循环依赖: {remaining}")
        
        return result
    
    def _get_ready_steps(self) -> List[str]:
        """
        获取可以执行的步骤（所有依赖都已完成）
        
        Returns:
            List[str]: 可执行的步骤列表
        """
        ready_steps = []
        
        for name, step in self.steps.items():
            if step.status != ExecutionStatus.PENDING:
                continue
            
            # 检查所有依赖是否已完成
            all_deps_completed = True
            for dep in step.dependencies:
                if dep not in self.steps:
                    continue
                
                dep_status = self.steps[dep].status
                if dep_status not in [ExecutionStatus.COMPLETED, ExecutionStatus.SKIPPED]:
                    all_deps_completed = False
                    break
            
            if all_deps_completed:
                ready_steps.append(name)
        
        return ready_steps
    
    async def _execute_step(self, step_name: str, context: ExecutionContext,
                           model_name: str = "default", provider_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        执行单个步骤
        
        Args:
            step_name: 步骤名称
            context: 执行上下文
            model_name: 使用的模型名称
            provider_name: 模型提供商名称，如果为None则使用默认提供商
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        step = self.steps[step_name]
        
        try:
            # 更新状态
            step.status = ExecutionStatus.RUNNING
            step.start_time = datetime.now()
            
            logger.info(f"开始执行步骤: {step_name}")
            
            # 执行描述器（内部会处理工具执行与上下文前后置更新）
            descriptor_result = await self.descriptor_executor.execute_descriptor(
                step.descriptor,
                context,
                model_name,
                provider_name
            )
            
            # 合并结果
            result = {
                "step_name": step_name,
                "descriptor_result": descriptor_result,
                "execution_time": step.duration
            }
            
            # 更新状态
            step.status = ExecutionStatus.COMPLETED
            step.result = result
            step.end_time = datetime.now()
            
            logger.info(f"步骤 {step_name} 执行完成，耗时: {step.duration:.2f}秒")
            
            return result
            
        except Exception as e:
            # 更新状态
            step.status = ExecutionStatus.FAILED
            step.error = str(e)
            step.end_time = datetime.now()
            
            logger.error(f"步骤 {step_name} 执行失败: {str(e)}")
            
            return {
                "step_name": step_name,
                "success": False,
                "error": str(e),
                "execution_time": step.duration
            }
    
    async def execute_workflow(
        self, 
        context: ExecutionContext,
        max_parallel: int = 3,
        fail_fast: bool = False,
        model_name: str = "default",
        provider_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        执行工作流
        
        Args:
            context: 执行上下文
            max_parallel: 最大并行数
            fail_fast: 是否快速失败
            model_name: 使用的模型名称
            provider_name: 模型提供商名称，如果为None则使用默认提供商
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        if not self.steps:
            return {
                "success": True,
                "message": "没有步骤需要执行",
                "results": {}
            }
        
        try:
            # 构建执行顺序
            self.execution_order = self._build_execution_order()
            logger.info(f"执行顺序: {self.execution_order}")
            
            # 执行统计
            start_time = datetime.now()
            results = {}
            errors = []
            running_tasks = {}
            
            while True:
                async with self._lock:
                    # 获取可执行的步骤
                    ready_steps = self._get_ready_steps()
                    
                    # 启动新任务
                    while (len(running_tasks) < max_parallel and 
                           ready_steps and 
                           len(ready_steps) > 0):
                        
                        step_name = ready_steps.pop(0)
                        task = asyncio.create_task(
                            self._execute_step(step_name, context, model_name, provider_name)
                        )
                        running_tasks[step_name] = task
                        logger.info(f"启动步骤任务: {step_name}")
                
                # 如果没有运行中的任务，退出循环
                if not running_tasks:
                    break
                
                # 等待至少一个任务完成
                done, pending = await asyncio.wait(
                    running_tasks.values(),
                    return_when=asyncio.FIRST_COMPLETED
                )
                
                # 处理完成的任务
                for task in done:
                    # 找到对应的步骤名称
                    step_name = None
                    for name, t in running_tasks.items():
                        if t == task:
                            step_name = name
                            break
                    
                    if step_name:
                        try:
                            result = await task
                            results[step_name] = result
                            
                            if not result.get("success", True):
                                errors.append(f"{step_name}: {result.get('error', '执行失败')}")
                                
                                if fail_fast:
                                    # 取消所有运行中的任务
                                    for pending_task in pending:
                                        pending_task.cancel()
                                    break
                                    
                        except Exception as e:
                            error_msg = f"{step_name}: {str(e)}"
                            errors.append(error_msg)
                            
                            if fail_fast:
                                for pending_task in pending:
                                    pending_task.cancel()
                                break
                        
                        # 从运行中任务中移除
                        del running_tasks[step_name]
                
                # 如果快速失败且有错误，退出循环
                if fail_fast and errors:
                    break
            
            # 计算总执行时间
            end_time = datetime.now()
            total_duration = (end_time - start_time).total_seconds()
            
            # 统计结果
            completed_count = sum(1 for step in self.steps.values() 
                                if step.status == ExecutionStatus.COMPLETED)
            failed_count = sum(1 for step in self.steps.values() 
                             if step.status == ExecutionStatus.FAILED)
            
            return {
                "success": len(errors) == 0,
                "total_steps": len(self.steps),
                "completed": completed_count,
                "failed": failed_count,
                "errors": errors,
                "results": results,
                "execution_time": total_duration,
                "execution_order": self.execution_order
            }
            
        except Exception as e:
            logger.error(f"工作流执行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "results": {}
            }
    
    def get_execution_status(self) -> Dict[str, Any]:
        """
        获取执行状态
        
        Returns:
            Dict[str, Any]: 执行状态信息
        """
        status_counts = {}
        for status in ExecutionStatus:
            status_counts[status.value] = sum(
                1 for step in self.steps.values() 
                if step.status == status
            )
        
        return {
            "total_steps": len(self.steps),
            "status_counts": status_counts,
            "steps": {
                name: {
                    "status": step.status.value,
                    "dependencies": step.dependencies,
                    "duration": step.duration,
                    "error": step.error
                }
                for name, step in self.steps.items()
            }
        }
    
    def reset(self):
        """重置所有步骤状态"""
        for step in self.steps.values():
            step.status = ExecutionStatus.PENDING
            step.result = None
            step.error = None
            step.start_time = None
            step.end_time = None
        
        logger.info("重置所有步骤状态")
