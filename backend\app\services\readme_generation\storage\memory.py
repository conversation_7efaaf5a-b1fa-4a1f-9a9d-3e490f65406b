"""
内存存储实现

提供基于内存的存储实现，用于开发和测试
"""

import asyncio
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone

from .base import BaseStorage
from ..models.logic_tree import LogicTree
from ..models.content import NodeContent
from ..models.project_context import ProjectContext


class MemoryStorage(BaseStorage):
    """内存存储实现"""
    
    def __init__(self):
        """初始化内存存储"""
        self._logic_trees: Dict[str, LogicTree] = {}
        self._node_contents: Dict[str, NodeContent] = {}
        self._project_contexts: Dict[str, ProjectContext] = {}
        self._node_content_index: Dict[str, str] = {}  # node_id -> content_id
        self._project_tree_index: Dict[str, List[str]] = {}  # project_id -> [tree_ids]
        self._tree_content_index: Dict[str, List[str]] = {}  # tree_id -> [content_ids]
        self._initialized = False
    
    async def initialize(self) -> bool:
        """初始化存储"""
        try:
            # 内存存储无需特殊初始化
            self._initialized = True
            return True
        except Exception:
            return False
    
    async def shutdown(self) -> None:
        """关闭存储"""
        # 清空所有数据
        self._logic_trees.clear()
        self._node_contents.clear()
        self._project_contexts.clear()
        self._node_content_index.clear()
        self._project_tree_index.clear()
        self._tree_content_index.clear()
        self._initialized = False
    
    # 逻辑树存储
    async def save_logic_tree(self, tree: LogicTree) -> bool:
        """保存逻辑树"""
        try:
            self._logic_trees[tree.id] = tree
            
            # 更新项目索引
            if tree.project_id not in self._project_tree_index:
                self._project_tree_index[tree.project_id] = []
            if tree.id not in self._project_tree_index[tree.project_id]:
                self._project_tree_index[tree.project_id].append(tree.id)
            
            return True
        except Exception:
            return False
    
    async def get_logic_tree(self, tree_id: str) -> Optional[LogicTree]:
        """获取逻辑树"""
        return self._logic_trees.get(tree_id)
    
    async def get_logic_trees_by_project(self, project_id: str) -> List[LogicTree]:
        """根据项目ID获取逻辑树列表"""
        tree_ids = self._project_tree_index.get(project_id, [])
        trees = []
        for tree_id in tree_ids:
            tree = self._logic_trees.get(tree_id)
            if tree:
                trees.append(tree)
        return trees

    async def get_all_logic_trees(self) -> List[LogicTree]:
        """获取全部逻辑树"""
        trees = list(self._logic_trees.values())
        # 按创建时间倒序排列
        trees.sort(key=lambda x: x.created_at or datetime.min, reverse=True)
        return trees

    async def delete_logic_tree(self, tree_id: str) -> bool:
        """删除逻辑树"""
        try:
            tree = self._logic_trees.get(tree_id)
            if not tree:
                return False
            
            # 删除相关的节点内容
            content_ids = self._tree_content_index.get(tree_id, [])
            for content_id in content_ids:
                await self.delete_node_content(content_id)
            
            # 从项目索引中移除
            if tree.project_id in self._project_tree_index:
                if tree_id in self._project_tree_index[tree.project_id]:
                    self._project_tree_index[tree.project_id].remove(tree_id)
            
            # 删除逻辑树
            del self._logic_trees[tree_id]
            
            # 清理索引
            if tree_id in self._tree_content_index:
                del self._tree_content_index[tree_id]
            
            return True
        except Exception:
            return False
    
    # 节点内容存储
    async def save_node_content(self, content: NodeContent) -> bool:
        """保存节点内容"""
        try:
            self._node_contents[content.id] = content
            
            # 更新节点索引
            self._node_content_index[content.node_id] = content.id
            
            # 更新树内容索引
            if content.tree_id not in self._tree_content_index:
                self._tree_content_index[content.tree_id] = []
            if content.id not in self._tree_content_index[content.tree_id]:
                self._tree_content_index[content.tree_id].append(content.id)
            
            return True
        except Exception:
            return False
    
    async def get_node_content(self, content_id: str) -> Optional[NodeContent]:
        """获取节点内容"""
        return self._node_contents.get(content_id)
    
    async def get_node_content_by_node(self, node_id: str) -> Optional[NodeContent]:
        """根据节点ID获取内容"""
        content_id = self._node_content_index.get(node_id)
        if content_id:
            return self._node_contents.get(content_id)
        return None
    
    async def get_node_contents_by_tree(self, tree_id: str) -> List[NodeContent]:
        """根据逻辑树ID获取所有节点内容"""
        content_ids = self._tree_content_index.get(tree_id, [])
        contents = []
        for content_id in content_ids:
            content = self._node_contents.get(content_id)
            if content:
                contents.append(content)
        return contents
    
    async def delete_node_content(self, content_id: str) -> bool:
        """删除节点内容"""
        try:
            content = self._node_contents.get(content_id)
            if not content:
                return False
            
            # 从节点索引中移除
            if content.node_id in self._node_content_index:
                del self._node_content_index[content.node_id]
            
            # 从树内容索引中移除
            if content.tree_id in self._tree_content_index:
                if content_id in self._tree_content_index[content.tree_id]:
                    self._tree_content_index[content.tree_id].remove(content_id)
            
            # 删除内容
            del self._node_contents[content_id]
            
            return True
        except Exception:
            return False
    
    # 项目上下文存储
    async def save_project_context(self, context: ProjectContext) -> bool:
        """保存项目上下文"""
        try:
            self._project_contexts[context.project_id] = context
            return True
        except Exception:
            return False
    
    async def get_project_context(self, project_id: str) -> Optional[ProjectContext]:
        """获取项目上下文"""
        return self._project_contexts.get(project_id)
    
    async def delete_project_context(self, project_id: str) -> bool:
        """删除项目上下文"""
        try:
            if project_id in self._project_contexts:
                del self._project_contexts[project_id]
                return True
            return False
        except Exception:
            return False
    
    # 查询和统计
    async def list_projects(self) -> List[str]:
        """列出所有项目ID"""
        return list(self._project_contexts.keys())
    
    async def get_storage_statistics(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        return {
            "type": "memory",
            "initialized": self._initialized,
            "logic_trees_count": len(self._logic_trees),
            "node_contents_count": len(self._node_contents),
            "project_contexts_count": len(self._project_contexts),
            "projects_count": len(self._project_tree_index),
            "timestamp": datetime.now(timezone.utc)
        }
    
    async def health_check(self) -> bool:
        """健康检查"""
        return self._initialized
