"""
步骤类型定义

定义通用的流程步骤类型系统，可扩展用于不同的业务场景
"""

from enum import Enum
from typing import Dict, Any, Optional, List


class StepType(str, Enum):
    """通用步骤类型枚举"""
    
    # 通用步骤类型
    INITIALIZATION = "initialization"      # 初始化
    DATA_COLLECTION = "data_collection"    # 数据收集
    DATA_ANALYSIS = "data_analysis"        # 数据分析
    PROCESSING = "processing"              # 处理
    VALIDATION = "validation"              # 验证
    OUTPUT_GENERATION = "output_generation" # 输出生成
    FINALIZATION = "finalization"          # 完成
    
    # 文档生成相关
    INTRODUCTION = "introduction"          # 介绍
    FEATURES = "features"                  # 功能特性
    INSTALLATION = "installation"          # 安装说明
    USAGE = "usage"                        # 使用说明
    API_DOCUMENTATION = "api_documentation" # API文档
    ARCHITECTURE = "architecture"          # 架构说明
    MERMAID_DIAGRAM = "mermaid_diagram"    # Mermaid架构图
    DEPLOYMENT = "deployment"              # 部署说明
    
    # 代码相关
    CODE_ANALYSIS = "code_analysis"        # 代码分析
    CODE_GENERATION = "code_generation"    # 代码生成
    CODE_REVIEW = "code_review"            # 代码审查
    TESTING = "testing"                    # 测试
    
    # 项目管理相关
    PLANNING = "planning"                  # 规划
    TASK_BREAKDOWN = "task_breakdown"      # 任务分解
    PROGRESS_TRACKING = "progress_tracking" # 进度跟踪
    REPORTING = "reporting"                # 报告
    
    # 自定义步骤
    CUSTOM = "custom"                      # 自定义步骤


class StepTypeRegistry:
    """步骤类型注册表"""
    
    _custom_types: Dict[str, Dict[str, Any]] = {}
    _type_metadata: Dict[StepType, Dict[str, Any]] = {}
    
    @classmethod
    def register_custom_type(
        cls,
        type_name: str,
        description: str,
        category: str = "custom",
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        注册自定义步骤类型
        
        Args:
            type_name: 类型名称
            description: 类型描述
            category: 类型分类
            metadata: 额外元数据
        """
        cls._custom_types[type_name] = {
            "description": description,
            "category": category,
            "metadata": metadata or {}
        }
    
    @classmethod
    def get_custom_type(cls, type_name: str) -> Optional[Dict[str, Any]]:
        """
        获取自定义步骤类型信息
        
        Args:
            type_name: 类型名称
            
        Returns:
            Optional[Dict[str, Any]]: 类型信息
        """
        return cls._custom_types.get(type_name)
    
    @classmethod
    def list_custom_types(cls) -> List[str]:
        """
        列出所有自定义步骤类型
        
        Returns:
            List[str]: 自定义类型列表
        """
        return list(cls._custom_types.keys())
    
    @classmethod
    def set_type_metadata(cls, step_type: StepType, metadata: Dict[str, Any]) -> None:
        """
        设置步骤类型元数据
        
        Args:
            step_type: 步骤类型
            metadata: 元数据
        """
        cls._type_metadata[step_type] = metadata
    
    @classmethod
    def get_type_metadata(cls, step_type: StepType) -> Dict[str, Any]:
        """
        获取步骤类型元数据
        
        Args:
            step_type: 步骤类型
            
        Returns:
            Dict[str, Any]: 元数据
        """
        return cls._type_metadata.get(step_type, {})
    
    @classmethod
    def get_type_description(cls, step_type: StepType) -> str:
        """
        获取步骤类型描述
        
        Args:
            step_type: 步骤类型
            
        Returns:
            str: 类型描述
        """
        descriptions = {
            StepType.INITIALIZATION: "初始化流程，设置环境和参数",
            StepType.DATA_COLLECTION: "收集所需的数据和信息",
            StepType.DATA_ANALYSIS: "分析收集到的数据",
            StepType.PROCESSING: "处理数据或执行核心逻辑",
            StepType.VALIDATION: "验证结果的正确性和完整性",
            StepType.OUTPUT_GENERATION: "生成最终输出",
            StepType.FINALIZATION: "完成流程，清理资源",
            
            StepType.INTRODUCTION: "生成介绍性内容",
            StepType.FEATURES: "描述功能特性",
            StepType.INSTALLATION: "提供安装指导",
            StepType.USAGE: "说明使用方法",
            StepType.API_DOCUMENTATION: "生成API文档",
            StepType.ARCHITECTURE: "描述系统架构",
            StepType.MERMAID_DIAGRAM: "生成Mermaid架构图",
            StepType.DEPLOYMENT: "提供部署指导",
            
            StepType.CODE_ANALYSIS: "分析代码结构和质量",
            StepType.CODE_GENERATION: "生成代码",
            StepType.CODE_REVIEW: "审查代码质量",
            StepType.TESTING: "执行测试",
            
            StepType.PLANNING: "制定计划",
            StepType.TASK_BREAKDOWN: "分解任务",
            StepType.PROGRESS_TRACKING: "跟踪进度",
            StepType.REPORTING: "生成报告",
            
            StepType.CUSTOM: "自定义步骤"
        }
        
        return descriptions.get(step_type, f"步骤类型: {step_type.value}")
    
    @classmethod
    def get_type_category(cls, step_type: StepType) -> str:
        """
        获取步骤类型分类
        
        Args:
            step_type: 步骤类型
            
        Returns:
            str: 类型分类
        """
        categories = {
            StepType.INITIALIZATION: "general",
            StepType.DATA_COLLECTION: "general",
            StepType.DATA_ANALYSIS: "general",
            StepType.PROCESSING: "general",
            StepType.VALIDATION: "general",
            StepType.OUTPUT_GENERATION: "general",
            StepType.FINALIZATION: "general",
            
            StepType.INTRODUCTION: "documentation",
            StepType.FEATURES: "documentation",
            StepType.INSTALLATION: "documentation",
            StepType.USAGE: "documentation",
            StepType.API_DOCUMENTATION: "documentation",
            StepType.ARCHITECTURE: "documentation",
            StepType.MERMAID_DIAGRAM: "documentation",
            StepType.DEPLOYMENT: "documentation",
            
            StepType.CODE_ANALYSIS: "development",
            StepType.CODE_GENERATION: "development",
            StepType.CODE_REVIEW: "development",
            StepType.TESTING: "development",
            
            StepType.PLANNING: "management",
            StepType.TASK_BREAKDOWN: "management",
            StepType.PROGRESS_TRACKING: "management",
            StepType.REPORTING: "management",
            
            StepType.CUSTOM: "custom"
        }
        
        return categories.get(step_type, "unknown")
    
    @classmethod
    def list_types_by_category(cls, category: str) -> List[StepType]:
        """
        按分类列出步骤类型
        
        Args:
            category: 分类名称
            
        Returns:
            List[StepType]: 该分类下的步骤类型列表
        """
        return [
            step_type for step_type in StepType
            if cls.get_type_category(step_type) == category
        ]


# 预设一些常用的步骤类型元数据
StepTypeRegistry.set_type_metadata(StepType.INITIALIZATION, {
    "required": True,
    "order": 0,
    "dependencies": []
})

StepTypeRegistry.set_type_metadata(StepType.DATA_COLLECTION, {
    "required": True,
    "order": 10,
    "dependencies": [StepType.INITIALIZATION]
})

StepTypeRegistry.set_type_metadata(StepType.FINALIZATION, {
    "required": True,
    "order": 1000,
    "dependencies": []
})
