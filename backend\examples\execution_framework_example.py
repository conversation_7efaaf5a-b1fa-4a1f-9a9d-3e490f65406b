#!/usr/bin/env python3
"""
执行框架使用示例

演示如何使用 execution_framework 来创建和执行AI驱动的工作流
"""

import asyncio
import logging
import sys
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Optional

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

# 导入执行框架组件
from app.services.execution_framework.execution_engine import ExecutionEngine
from app.services.execution_framework.base.descriptor import BaseDescriptor
from app.services.execution_framework.base.step_type import StepType
from app.services.execution_framework.models.context import ExecutionContext, ExecutionContextBuilder
from app.services.execution_framework.models.descriptor_config import DescriptorConfig
from app.services.execution_framework.models.tool_instruction import ToolInstruction

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ProjectAnalysisDescriptor(BaseDescriptor):
    """项目分析描述器示例"""
    
    def __init__(self):
        super().__init__(StepType.CODE_ANALYSIS, "project_analysis")
    
    def get_descriptor_config(self, context: ExecutionContext) -> Optional[DescriptorConfig]:
        """获取描述器配置"""
        return DescriptorConfig(
            template="""
            你需要对当前项目进行全面分析。请按照以下步骤进行：

            1. 首先使用 view-directory 工具查看项目的目录结构，了解项目的整体组织方式和主要文件
            2. 然后使用 codebase-retrieval 工具深入分析项目的技术信息

            基于获取的信息，请提供详细的项目分析报告，包括：
            - 项目概述和主要功能
            - 技术栈和架构分析
            - 代码质量评估
            - 依赖关系分析
            - 改进建议和最佳实践

            请以结构化的方式呈现分析结果，确保信息准确且易于理解。
            """,
            input_variables=[],
            system_prompt="你是一个专业的项目分析师，擅长分析软件项目的架构、代码质量和技术栈。你可以使用提供的工具来获取项目信息，然后进行专业分析。",
            output_format="structured_analysis",
            temperature=0.3,
            max_tokens=2000
        )
    
    def get_tool_instructions(self, context: ExecutionContext) -> List[ToolInstruction]:
        """获取工具调用指令列表"""
        instructions = []

        # 1. 查看项目结构和文件
        instructions.append(
            ToolInstruction(
                name="view-directory",
                parameters={
                    "path": context.workspace_path or "."
                },
                description=f"查看项目目录结构，分析项目组织方式和主要文件: {context.workspace_path or '.'}",
                required=True,
                order=1,
                result_key="project_analysis"
            )
        )

        # 2. 检索代码库完整信息
        instructions.append(
            ToolInstruction(
                name="codebase-retrieval",
                parameters={
                    "information_request": "项目的技术栈、架构设计、核心功能模块、主要类和函数实现、API接口定义、数据模型结构、依赖关系、配置文件内容"
                },
                description="使用AI检索分析项目的完整技术信息和代码实现细节",
                required=True,
                order=2,
                result_key="codebase_analysis"
            )
        )

        return instructions


class DocumentationDescriptor(BaseDescriptor):
    """文档生成描述器示例"""
    
    def __init__(self):
        super().__init__(StepType.OUTPUT_GENERATION, "documentation_generator")
    
    def get_descriptor_config(self, context: ExecutionContext) -> Optional[DescriptorConfig]:
        """获取描述器配置"""
        return DescriptorConfig(
            template="""
            你需要为当前项目生成完整的技术文档。请按照以下步骤进行：

            1. 首先使用 view-directory 工具查看项目结构，检查现有的文档文件（如 README.md、LICENSE 等）
            2. 然后使用 codebase-retrieval 工具获取项目的详细信息，包括功能特性、使用示例、配置说明等

            基于获取的信息，请生成包含以下内容的完整 README 文档：
            - 项目简介和主要功能
            - 技术栈和依赖说明
            - 安装和配置指南
            - 使用说明和示例
            - 项目结构说明
            - API 文档（如果适用）
            - 贡献指南
            - 许可证信息

            请使用标准的 Markdown 格式，确保文档结构清晰、内容准确且易于理解。
            """,
            input_variables=[],
            system_prompt="你是一个专业的技术文档编写专家，擅长编写清晰易懂的项目文档。你可以使用提供的工具来获取项目信息，然后生成高质量的技术文档。",
            output_format="markdown",
            temperature=0.5,
            max_tokens=3000
        )
    
    def get_tool_instructions(self, context: ExecutionContext) -> List[ToolInstruction]:
        """获取工具调用指令列表"""
        instructions = []

        # 1. 查看项目文档和配置文件
        instructions.append(
            ToolInstruction(
                name="view-directory",
                parameters={
                    "path": context.workspace_path or "."
                },
                description="查看项目结构，检查现有文档文件（README、LICENSE等）",
                required=True,
                order=1,
                result_key="project_docs"
            )
        )

        # 2. 检索项目的完整信息用于文档生成
        instructions.append(
            ToolInstruction(
                name="codebase-retrieval",
                parameters={
                    "information_request": "项目的功能特性、使用示例、测试文件、配置示例、部署相关文件、API文档、安装说明"
                },
                description="检索项目的完整信息，包括使用示例、测试代码和部署说明，用于生成完整文档",
                required=True,
                order=2,
                result_key="documentation_info"
            )
        )

        return instructions


class CodeRefactoringDescriptor(BaseDescriptor):
    """代码重构描述器示例 - 展示更多工具的使用"""

    def __init__(self):
        super().__init__(StepType.CODE_GENERATION, "code_refactoring")

    def get_descriptor_config(self, context: ExecutionContext) -> Optional[DescriptorConfig]:
        """获取描述器配置"""
        return DescriptorConfig(
            template="""
            你需要对指定的代码文件进行重构优化。请按照以下步骤进行：

            1. 首先使用 view-file 工具查看目标文件 "app/services/example_service.py" 的当前代码内容
            2. 然后使用 codebase-retrieval 工具分析该文件的代码上下文、依赖关系和质量问题
            3. 最后使用 save-file 工具保存重构后的代码到新文件

            在重构过程中，请重点关注：
            - 代码可读性和可维护性改进
            - 性能优化和效率提升
            - 最佳实践和设计模式应用
            - 错误处理和异常管理改进
            - 代码复用和模块化
            - 类型注解和文档字符串完善

            请确保重构后的代码保持原有功能不变，同时提升代码质量。
            """,
            input_variables=[],
            system_prompt="你是一个专业的代码重构专家，擅长改进代码质量和性能。你可以使用提供的工具来分析代码并生成重构后的版本。",
            output_format="code",
            temperature=0.2,
            max_tokens=4000
        )

    def get_tool_instructions(self, context: ExecutionContext) -> List[ToolInstruction]:
        """获取工具调用指令列表"""
        instructions = []

        # 1. 查看目标文件
        instructions.append(
            ToolInstruction(
                name="view-file",
                parameters={
                    "path": "app/services/example_service.py"
                },
                description="查看需要重构的目标文件内容",
                required=True,
                order=1,
                result_key="target_file_content"
            )
        )

        # 2. 检索代码分析信息
        instructions.append(
            ToolInstruction(
                name="codebase-retrieval",
                parameters={
                    "information_request": "example_service.py的代码上下文、依赖关系、质量问题、性能瓶颈、重复代码和改进机会"
                },
                description="全面分析目标文件的代码质量和重构机会",
                required=True,
                order=2,
                result_key="refactoring_analysis"
            )
        )

        # 3. 保存重构后的文件
        instructions.append(
            ToolInstruction(
                name="save-file",
                parameters={
                    "path": "app/services/example_service_refactored.py",
                    "file_content": "# 这里将由LLM根据分析结果生成重构后的代码",
                    "instructions_reminder": "LIMIT THE FILE CONTENT TO AT MOST 300 LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED."
                },
                description="保存重构后的代码到新文件，LLM需要根据前面的分析结果生成完整的重构代码",
                required=True,
                order=3,
                result_key="refactored_file"
            )
        )

        return instructions


async def basic_single_descriptor_example():
    """基础单描述器执行示例"""
    print("\n=== 基础单描述器执行示例 ===")

    # 创建执行引擎
    engine = ExecutionEngine()

    # 创建并注册描述器
    project_descriptor = ProjectAnalysisDescriptor()
    engine.register_descriptor(project_descriptor)

    # 创建执行上下文
    context = ExecutionContextBuilder.create_basic_context(
        process_id="example_process",
        process_name="项目分析示例",
        process_type="code_analysis",
        workspace_path="."
    )

    try:
        # 执行单个描述器
        result = await engine.execute_single_descriptor(
            "project_analysis",
            context,
            model_name="gemini-2.5-flash"
        )

        print(f"执行结果: {result}")

    except Exception as e:
        print(f"执行失败: {e}")


async def workflow_execution_example():
    """工作流执行示例"""
    print("\n=== 工作流执行示例 ===")

    # 创建执行引擎
    engine = ExecutionEngine()

    # 创建并注册多个描述器
    project_descriptor = ProjectAnalysisDescriptor()
    doc_descriptor = DocumentationDescriptor()

    engine.register_descriptor(project_descriptor)
    engine.register_descriptor(doc_descriptor)

    # 创建执行上下文
    context = ExecutionContextBuilder.create_basic_context(
        process_id="workflow_process",
        process_name="工作流示例",
        process_type="workflow",
        workspace_path="."
    )

    try:
        # 定义工作流依赖关系
        dependencies = {
            "documentation_generator": ["project_analysis"]  # 文档生成依赖项目分析
        }

        # 执行工作流
        result = await engine.execute_workflow(
            descriptors=[project_descriptor, doc_descriptor],
            context=context,
            dependencies=dependencies,
            max_parallel=2
        )

        print(f"工作流执行结果:")
        print(f"- 成功: {result['success']}")
        print(f"- 总步骤: {result['total_steps']}")
        print(f"- 完成: {result['completed']}")
        print(f"- 失败: {result['failed']}")
        print(f"- 执行时间: {result['execution_time']:.2f}秒")

        if result['errors']:
            print(f"- 错误: {result['errors']}")

    except Exception as e:
        print(f"工作流执行失败: {e}")


async def auto_execution_example():
    """自动执行示例"""
    print("\n=== 自动执行示例 ===")

    # 创建执行引擎
    engine = ExecutionEngine()

    # 注册多个不同类型的描述器
    engine.register_descriptor(ProjectAnalysisDescriptor())
    engine.register_descriptor(DocumentationDescriptor())
    engine.register_descriptor(CodeRefactoringDescriptor())

    # 创建执行上下文
    context = ExecutionContextBuilder.create_basic_context(
        process_id="auto_process",
        process_name="自动执行示例",
        process_type="auto_execution",
        workspace_path="."
    )

    try:
        # 自动执行 - 引擎会自动选择适用的描述器
        result = await engine.auto_execute(
            context=context,
            step_types=[StepType.CODE_ANALYSIS, StepType.OUTPUT_GENERATION, StepType.CODE_GENERATION],
            max_descriptors_per_type=2
        )

        print(f"自动执行结果:")
        print(f"- 成功: {result['success']}")
        print(f"- 执行的描述器: {list(result['results'].keys())}")
        print(f"- 执行时间: {result['execution_time']:.2f}秒")

    except Exception as e:
        print(f"自动执行失败: {e}")


async def monitoring_example():
    """监控功能示例"""
    print("\n=== 监控功能示例 ===")

    # 创建执行引擎
    engine = ExecutionEngine()
    engine.register_descriptor(ProjectAnalysisDescriptor())

    # 创建执行上下文
    context = ExecutionContextBuilder.create_basic_context(
        process_id="monitoring_process",
        process_name="监控示例",
        process_type="monitoring",
        workspace_path="."
    )

    try:
        # 执行描述器
        await engine.execute_single_descriptor("project_analysis", context)

        # 获取性能统计
        stats = engine.execution_monitor.get_performance_stats()
        print(f"性能统计: {stats}")

        # 获取最近的指标
        metrics = engine.execution_monitor.get_recent_metrics(limit=5)
        print(f"最近指标数量: {len(metrics)}")

        # 获取最近的事件
        events = engine.execution_monitor.get_recent_events(limit=5)
        print(f"最近事件数量: {len(events)}")

    except Exception as e:
        print(f"监控示例失败: {e}")


async def main():
    """主函数 - 运行所有示例"""
    print("执行框架使用示例")
    print("=" * 50)
    
    try:
        # 运行各种示例
        await basic_single_descriptor_example()
        await workflow_execution_example()
        await auto_execution_example()
        await monitoring_example()
        
        print("\n所有示例执行完成！")
        
    except Exception as e:
        logger.error(f"示例执行失败: {e}")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
