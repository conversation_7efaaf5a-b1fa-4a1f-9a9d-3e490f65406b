#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/9/17 11:47
# @File    : arxiv_handler.py
# @Description: 
"""

import structlog
import json
from typing import Dict, Any
from tornado.web import HTTPError

from app.api.base import BaseHandler
from app.core.di.containers import Container
from app.core.middleware import require_auth
from app.services.rag.arxiv_processor_service import ArxivProcessorService
from app.schemas.rag.arxiv import (
    ArxivProcessRequest, ArxivSearchRequest
)
from dependency_injector.wiring import inject, Provide

logger = structlog.get_logger(__name__)


class ArxivProcessHandler(BaseHandler):
    """arXiv论文处理接口"""

    @inject
    def initialize(
            self,
            container: Container = Provide[Container]
    ):
        """初始化处理器"""
        self.container = container
        self.arxiv_service = ArxivProcessorService(container)

    @require_auth
    async def post(self):
        """
        开始处理arXiv文件

        请求体:
        {
            "max_files": 100,
            "max_papers_per_file": 100,
            "embedding_model": "tei_bge_base_zh",
            "vector_store": "milvus_quality",
            "chunk_size": 1000,
            "chunk_overlap": 200
        }
        """
        try:
            # 解析请求
            request_data = json.loads(self.request.body)
            request = ArxivProcessRequest(**request_data)

            # 初始化服务
            if not await self.arxiv_service.initialize():
                raise HTTPError(500, "服务初始化失败")

            # 开始处理
            response = await self.arxiv_service.process_arxiv_files(request)

            self.write_json({
                'code': 200,
                'message': 'success',
                'data': response.dict()
            })

        except ValueError as e:
            logger.error(f"请求参数错误: {e}")
            raise HTTPError(400, f"请求参数错误: {str(e)}")
        except Exception as e:
            logger.error(f"处理arXiv文件时发生错误: {e}")
            raise HTTPError(500, f"处理失败: {str(e)}")


class ArxivSearchHandler(BaseHandler):
    """arXiv论文搜索接口"""

    @inject
    def initialize(
            self,
            container: Container = Provide[Container]
    ):
        """初始化处理器"""
        self.container = container
        self.arxiv_service = ArxivProcessorService(container)

    async def post(self):
        """
        搜索arXiv论文

        请求体:
        {
            "query": "machine learning",
            "top_k": 10,
            "similarity_threshold": 0.7,
            "search_type": "semantic"
        }
        """
        try:
            # 解析请求
            request_data = json.loads(self.request.body)
            request = ArxivSearchRequest(**request_data)

            # 初始化服务
            if not await self.arxiv_service.initialize():
                raise HTTPError(500, "服务初始化失败")

            # 执行搜索
            response = await self.arxiv_service.search_papers(request)

            self.write_json({
                'code': 200,
                'message': 'success',
                'data': response.dict()
            })

        except ValueError as e:
            logger.error(f"请求参数错误: {e}")
            raise HTTPError(400, f"请求参数错误: {str(e)}")
        except Exception as e:
            logger.error(f"搜索论文时发生错误: {e}")
            raise HTTPError(500, f"搜索失败: {str(e)}")


class ArxivStatusHandler(BaseHandler):
    """arXiv处理状态查询接口"""

    @inject
    def initialize(
            self,
            container: Container = Provide[Container]
    ):
        """初始化处理器"""
        self.container = container
        self.arxiv_service = ArxivProcessorService(container)

    async def get(self):
        """
        获取arXiv处理状态统计
        """
        try:
            # 获取处理状态
            status = await self.arxiv_service.get_processing_status()

            self.write_json({
                'code': 200,
                'message': 'success',
                'data': status
            })

        except Exception as e:
            logger.error(f"获取处理状态时发生错误: {e}")
            raise HTTPError(500, f"获取状态失败: {str(e)}")