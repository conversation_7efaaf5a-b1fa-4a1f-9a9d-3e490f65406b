"""
执行器模块

包含AI驱动执行引擎的所有核心组件
"""

from ..tools import (
    ToolManager,
    BaseToolHandler,
    CodebaseRetrievalHandler,
    ViewFileHandler,
    SaveFileHandler,
    RemoveFilesHandler,
    StrReplaceEditorHandler,
    WebSearchHandler,
    WebFetchHandler,
    ProcessHandler
)

from .descriptor_executor import (
    DescriptorExecutor
)

from .tool_wrapper import (
    DescriptorToolWrapper,
    ExecutionFrameworkToolWrapper
)

from .workflow_orchestrator import (
    WorkflowOrchestrator,
    ExecutionStep,
    ExecutionStatus
)

from .monitoring import (
    ExecutionMonitor,
    MetricsCollector,
    EventLogger,
    PerformanceMonitor,
    ExecutionMetric,
    ExecutionEvent,
    MetricType
)

__all__ = [
    # 工具管理
    "ToolManager",
    "BaseToolHandler",
    "CodebaseRetrievalHandler",
    "ViewFileHandler",
    "SaveFileHandler",
    "RemoveFilesHandler",
    "StrReplaceEditorHandler",
    "WebSearchHandler",
    "WebFetchHandler",
    "ProcessHandler",

    # 描述器执行
    "DescriptorExecutor",

    # 工具封装器
    "DescriptorToolWrapper",
    "ExecutionFrameworkToolWrapper",

    # 工作流编排
    "WorkflowOrchestrator",
    "ExecutionStep",
    "ExecutionStatus",

    # 监控和日志
    "ExecutionMonitor",
    "MetricsCollector",
    "EventLogger",
    "PerformanceMonitor",
    "ExecutionMetric",
    "ExecutionEvent",
    "MetricType"
]
