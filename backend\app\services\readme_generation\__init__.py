"""
README生成服务

提供按节点可调用的独立README生成服务，支持：
1. 逻辑树生成：基于项目结构/依赖/架构生成README逻辑树
2. 节点内容生成：对指定节点生成内容，支持版本历史
3. 仓库存储：保存逻辑树、节点内容及版本
4. 再次生成：支持选择生成模型、修改提示词
"""

from .generation_manager import GenerationManager, generation_manager
from .models.logic_tree import LogicTree, LogicNode, NodeType
from .models.content import NodeContent, ContentVersion, ContentStatus
from .models.project_context import ProjectContext
from .storage.base import BaseStorage
from .storage.memory import MemoryStorage

__all__ = [
    'GenerationManager',
    'generation_manager',
    'LogicTree',
    'LogicNode',
    'NodeType',
    'NodeContent',
    'ContentVersion',
    'ContentStatus',
    'ProjectContext',
    'BaseStorage',
    'MemoryStorage'
]
