"""
逻辑树处理器
"""

import json
from typing import Dict, Any, Optional
from tornado.web import HTTPError
import structlog
from dependency_injector.wiring import inject, Provide

from app.api.base import BaseHandler
from app.core.di.containers import Container
from app.services.readme_generation.generation_manager import GenerationManager

logger = structlog.get_logger(__name__)


class LogicTreeHandler(BaseHandler):
    """逻辑树处理器"""

    @inject
    def initialize(
        self,
        readme_generation_manager: GenerationManager = Provide[Container.readme_generation_manager],
    ):
        super().initialize()
        self.readme_generation_manager = readme_generation_manager

    async def post(self):
        """生成逻辑树"""
        try:
            if not self.json_body:
                raise HTTPError(400, "请求体不能为空")

            project_path = self.json_body.get("project_path")
            project_name = self.json_body.get("project_name")
            project_url = self.json_body.get("project_url")
            model_name = self.json_body.get("model_name", "default")
            provider_name = self.json_body.get("provider_name")
            custom_prompt = self.json_body.get("custom_prompt")

            if not project_path:
                raise HTTPError(400, "project_path参数是必需的")

            # 直接调用生成逻辑树（内部已集成项目分析逻辑）
            result = await self.readme_generation_manager.generate_logic_tree(
                project_path=project_path,
                project_name=project_name,
                project_url=project_url,
                model_name=model_name,
                provider_name=provider_name,
                custom_prompt=custom_prompt
            )

            if not result.get("success"):
                error_msg = result.get("error", "逻辑树生成失败")
                raise HTTPError(500, error_msg)

            # 返回生成结果
            response_data = {
                "tree_id": result.get("tree_id"),
                "project_id": result.get("project_id"),
                "message": result.get("message", "逻辑树生成成功")
            }

            self.success_response(response_data, "逻辑树生成成功")

        except HTTPError:
            raise
        except Exception as e:
            logger.error("逻辑树生成失败", error=str(e), exc_info=e)
            raise HTTPError(500, f"逻辑树生成失败: {str(e)}")
    
    async def get(self, tree_id: str):
        """获取逻辑树详情"""
        try:
            # 这里需要添加获取逻辑树的方法
            tree = await self.readme_generation_manager.get_logic_tree(tree_id)
            self.success_response({"tree": tree.model_dump()}, "逻辑树获取成功")
            
        except Exception as e:
            logger.error("获取逻辑树失败", error=str(e), exc_info=e)
            raise HTTPError(500, f"获取逻辑树失败: {str(e)}")
