<template>
  <div class="article-mobile" ref="articleContainerRef">
    <div class="article" ref="articleRef" id="article">
      <div class="title">{{ article.title }}</div>
      <div class="summary">{{ article.summary }}</div>
      <div class="tag">
        <div
          v-for="(item, index) in article.tags"
          :key="index"
          class="tag-item"
          @click="handleTags(item)">
          {{ item }}
        </div>
      </div>
      <div class="meta">
        <img src="@/assets/images/icon25.png" />
        <span>{{ dayjs(article.created_at).format("YYYY/MM/DD") }}</span>
        <img src="@/assets/images/icon26.png" />
        <span>{{ article.read_count }}</span>
      </div>

      <div class="article-detail">
        <MarkdownPreview
          class="content"
          :text="article.content"></MarkdownPreview>
      </div>
      <div style="margin-top: 15px">
        <div class="comment-drawer-content" ref="commentDrawerRef">
          <el-mention
            ref="textareaRef"
            class="textarea"
            v-model="textarea"
            :autosize="{ minRows: 3, maxRows: 8 }"
            maxlength="1000"
            type="textarea"
            show-word-limit
            placeholder="发表评论"
            :options="options"
            @focus="focus"
            @blur="blur"
            @input="handleInput" />
          <img
            class="emoji"
            src="@/assets/images/guguda.png"
            style="left: 40px"
            @click.stop="handleAiTe" />
          <el-popover
            placement="bottom"
            :width="306"
            popper-class="emoji-popover">
            <div>
              <EmojiPicker
                :hide-search="true"
                :native="true"
                @select="onSelectEmoji"
                :disabled-groups="[
                  'travel_places',
                  'flags',
                  'symbols',
                  'objects',
                  'activities',
                ]"
                :hide-group-names="true" />
            </div>
            <template #reference>
              <img
                class="emoji"
                src="@/assets/images/emoji.png"
                @click.stop="handleEmoji" />
            </template>
          </el-popover>

          <div class="btns">
            <el-button
              class="button"
              type="primary"
              :disabled="disabled"
              @click.stop="handleSend">
              发送
            </el-button>
          </div>
        </div>
        <div class="comment-container" v-if="msgList.length">
          <div style="font-size: 14px; color: #000000; margin-bottom: 10px">
            评论&nbsp;&nbsp;{{ article.comment_count }}条
          </div>
          <comment
            :data="msgList"
            :sendName="''"
            :articleId="query.id"
            :rootIds="rootIds"
            :commentIndex="commentIndex" />
          <div
            v-if="commentIndex['rootIndex'] > userStore.allMsgStatus.root"
            class="show-all-comment"
            @click="handleShowAllComment">
            <span>查看全部评论</span>
            <el-icon class="icon"><ArrowDown /></el-icon>
          </div>
        </div>
      </div>
      <div
        class="related-projects"
        v-if="article.related_projects && article.related_projects.length">
        <div
          style="
            padding-bottom: 6px;
            padding-left: 24px;
            border-bottom: 1px solid #ecf0f1;
          ">
          相关项目
        </div>
        <!-- 榜单内容 -->
        <ul class="ranking-list__content">
          <li
            v-for="(item, index) in article.related_projects"
            :key="index"
            class="ranking-list__item">
            <span class="ranking-list__rank">
              <template v-if="index < 3">
                <img
                  class="ranking-list__rank-icon"
                  :src="getRankIcon(index)"
                  alt="" />
              </template>
              <template v-else>
                {{ index + 1 }}
              </template>
            </span>
            <a
              @click.prevent="
                handleLink(
                  checkJson(item) ? checkJson(item).url : checkJson(item)
                )
              "
              target="_blank"
              rel="noopener noreferrer"
              class="ranking-list__item-content">
              {{ checkJson(item).title }}
            </a>
          </li>
        </ul>
      </div>
    </div>
    <div class="action-bar" v-if="barStatus">
      <div class="action-item">
        <img
          src="@/assets/images/icon27.png"
          style="width: 16px"
          @click="hrefTextarea" />
      </div>
      <div class="action-item" @click.stop="handleCollect">
        <Button1
          class="meta-favorite-icon"
          width="18px"
          height="18px"
          gray
          :filled="article.is_collected"></Button1>
      </div>
      <div class="action-item" @click.stop="handleCollect">
        <img src="@/assets/images/qrcode2.png" style="width: 16px" />
      </div>
      <div class="action-item" @click.stop="handleCollect">
        <img src="@/assets/images/icon04.png" style="width: 16px" />
      </div>
    </div>
    <HomeFooter style="background-color: transparent"></HomeFooter>
    <el-backtop
      :right="20"
      :bottom="110"
      :visibility-height="300"
      target=".article-mobile"
      style="z-index: 99" />
  </div>
</template>

<script setup>
// import WxQrCode from "@/components/WxQrCode";
// import Share from "@/components/Share/index.vue";
import rank1 from "@/assets/images/icon37.png";
import rank2 from "@/assets/images/icon38.png";
import rank3 from "@/assets/images/icon39.png";
import EmojiPicker from "vue3-emoji-picker";
import "vue3-emoji-picker/css";
import comment from "@/views/broadcast/article/comment.vue";
import HomeFooter from "@/layout/components/HomeFooter/index.vue";
import MarkdownPreview from "@/components/MarkdownPreview";
import Button1 from "@/components/Button/index1.vue";

import dayjs from "dayjs";

import useUserStore from "@/store/modules/user";
import { getToken } from "@/utils/auth";
import { ElMessage } from "element-plus";
import { getArticleList, commentTree, commentCreat } from "@/api/article";
import { addUserCollect, deleteUserCollect } from "@/api/broadcast";
import { getCurrentInstance, useTemplateRef, watch } from "vue";
const { query } = useRoute();
const router = useRouter();
const userStore = useUserStore();
const textareaRef = useTemplateRef("textareaRef");
const commentDrawerRef = useTemplateRef("commentDrawerRef");
const { proxy } = getCurrentInstance();
const article = ref({});
const msgList = ref([]);
const barStatus = ref(true);

const color = ref("#e4e6eb"); // 输入框背景颜色
const disabled = ref(true); // 输入框是否禁用
const options = ref([
  {
    label: "咕咕答",
    value: "咕咕答",
  },
]);
const initArticle = () => {
  getArticleList({
    article_id: query.id,
  }).then((res) => {
    if (res.code === 200) {
      article.value = res.data;
    }
  });

  getComment();
};

const getComment = () => {
  commentTree({
    article_id: query.id,
    page: 1,
    page_size: 50,
    depth: 10,
  }).then((res) => {
    if (res.code === 200) {
      flattenTree(res.data.comments);
      msgList.value = res.data.comments;
    }
  });
};

// 获取排名图标
const getRankIcon = (index) => {
  const icons = [rank1, rank2, rank3];
  return icons[index];
};

const handleShowComment = () => {
  if (getToken()) {
    // userStore.showCommentInput = true;
    userStore.replyName = "";
    textarea.value = "";
    nextTick(() => {
      textareaRef.value.input.focus();
    });
  } else {
    ElMessage({
      message: "请登录后评论",
      type: "warning",
    });
    router.push("/mobileLogin");
  }
};

const textarea = ref("");
const handleSend = () => {
  commentCreat({
    content: textarea.value,
    parent_id: "",
    project_id: query.id,
  }).then((res) => {
    if (res.code === 200) {
      userStore.showCommentInput = false;
      textarea.value = "";
      userStore.refreshComment = true;
      const timer = setTimeout(() => {
        userStore.refreshComment = false;
        clearTimeout(timer);
      }, 1000);
    }
  });
};
/**
 * 处理json字符串报错
 * @param str json字符串
 */
const checkJson = (str) => {
  try {
    return JSON.parse(str);
  } catch (error) {
    return "";
  }
};
watch(
  () => userStore.refreshComment,
  (newVal) => {
    if (newVal) {
      getComment();
    }
  }
);

watch(
  () => userStore.showCommentInput,
  (newVal) => {
    if (!newVal) {
      textarea.value = "";
    }
  }
);

const focus = () => {
  color.value = "#1e80ff";
  userStore.replyName = "";
  userStore.showCommentInput = true;
};

const blur = () => {
  color.value = "#e4e6eb";
};

const handleInput = () => {
  if (textarea.value) {
    disabled.value = false;
  } else {
    disabled.value = true;
  }
};
/**
 * 用户收藏
 */
const handleCollect = async () => {
  // 判断用户是否登录，如果为登录，则提示用户去登录
  if (!userStore?.name) {
    proxy.$modal.msgWarning("登录后即可收藏");
    return;
  }
  let res = {};
  if (article.value.is_collected) {
    res = await deleteUserCollect({ article_id: article.value.id });
    if (res.code === 200) {
      proxy.$modal.msgSuccess("已取消收藏");
      article.value.is_collected = !article.value.is_collected;
      article.value.collect_count--;
    }
  } else {
    res = await addUserCollect({ article_id: article.value.id });
    if (res.code === 200) {
      proxy.$modal.msgSuccess("已收藏");
      article.value.is_collected = !article.value.is_collected;
      article.value.collect_count++;
    }
  }
};

const handleLink = (url) => {
  if (url) {
    window.open(url);
  }
};

const handleEmoji = () => {
  textareaRef.value.input.focus();
};

const handleAiTe = () => {
  textarea.value = textarea.value + "@咕咕答";
  textareaRef.value.input.focus();
};

const onSelectEmoji = (emoji) => {
  textarea.value = textarea.value + emoji.i;
  textareaRef.value.input.focus();
};

const rootIds = ref([]); // 存储所有根节点的id
const commentIndex = ref({}); // 存储每个根节点的数量
const flattenTree = (tree, parentId = null, level = 0) => {
  let objIndex = {
    rootIndex: 0,
  };
  const traverse = (node, parentId, level) => {
    if (node.root_id === node.id) {
      node.index = objIndex.rootIndex;
      objIndex.rootIndex += 1;
      rootIds.value.push(node.id);
    } else {
      if (objIndex[node.root_id] === undefined) {
        objIndex[node.root_id] = 0;
      }
      node.index = objIndex[node.root_id];
      objIndex[node.root_id] += 1;
    }

    // 递归处理子节点
    if (node.replies && node.replies.length > 0) {
      node.replies.forEach((child) => {
        traverse(child, node.id, level + 1);
      });
    }
  };

  // 如果 tree 是数组，则遍历每个根节点
  if (Array.isArray(tree)) {
    tree.forEach((root, index) => traverse(root, null, 0, index));
  } else {
    // 如果 tree 是单个根节点
    traverse(tree, null, 0, index);
  }
  commentIndex.value = objIndex;
};

const handleShowAllComment = () => {
  userStore.allMsgStatus["root"] = 99;
};

const hrefTextarea = () => {
  commentDrawerRef.value.scrollIntoView({ block: "center" });
};
/**
 * 标签跳转到首页进行搜索
 * @param item
 */
const handleTags = (item) => {
  router.push({
    path: "/broadcast/mobileSearch",
    query: { searchKeyMobile: item, tab: "zixun" },
  });
};
const articleRef = useTemplateRef("articleRef");
const articleContainerRef = useTemplateRef("articleContainerRef");

onMounted(() => {
  initArticle();
  articleRef.value.style.minHeight =
    articleContainerRef.value.clientHeight - 40 + "px";
  if (query.commentId) {
    setTimeout(() => {
      document
        .getElementById(query.commentId)
        .scrollIntoView({ block: "center" });
    }, 500);
  }
});
userStore.showCommentInput = false;
</script>
<style scoped lang="scss">
::v-deep(.v3-emoji-picker .v3-footer) {
  display: none;
}
::v-deep(.v3-emoji-picker .v3-header) {
  display: none;
}
::v-deep(.v3-emoji-picker) {
  box-shadow: none;
}
.article-mobile {
  position: fixed;
  top: calc(var(--navbar-height));
  bottom: 0;
  left: 0;
  right: 0;
  overflow: auto;
}
.article {
  padding: 25px 12px 0;
  .title {
    width: 80%;
    white-space: nowrap; /* 禁止换行 */
    overflow: hidden; /* 隐藏溢出内容 */
    text-overflow: ellipsis; /* 显示省略号 */
    font-size: 18px;
    color: #4c6d7d;
    font-weight: 800;
  }

  .summary {
    font-weight: 400;
    font-size: 14px;
    color: #3f4a54;
    margin-top: 2px;
  }
  img {
    width: 16px;
    margin-right: 5px;
  }

  span {
    font-size: 14px;
    color: #656464;
  }
  .meta {
    display: flex;
    align-items: center;
    margin-top: 8px;
    font-size: 14px;
    color: #656464;
    img {
      width: 16px;
      margin-right: 4px;
    }

    span {
      margin-right: 25px;
    }
  }

  .tag {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 4px 6px;
    font-size: 12px;
    color: #60848d;

    .tag-item {
      padding: 2px 4px;
      border-radius: 4px 4px 4px 4px;
      font-weight: 400;
      font-size: 14px;
      color: #6e92a4;
      background: #ffffff;
      border: 1px solid #c0d9dd;
    }
  }

  .article-detail {
    margin: 6px 0;
    padding: 5px 12px;
    padding-bottom: 15px;
    background: #fffefe;
    box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    border: 1px solid #ecf0f1;

    .content {
      ::v-deep(.md-editor-preview) {
        font-size: 14px;
        color: #656464;
      }
    }
  }

  .comment-container {
    margin-top: 15px;

    .show-all-comment {
      height: 40px;
      margin-top: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      border-radius: 4px;
      background-color: #f7f8fa;
      cursor: pointer;

      &:hover {
        background-color: #e8e8e8;
      }

      .icon {
        margin-top: 2px;
        margin-left: 4px;
      }
    }
  }
}

.comment-drawer-content {
  position: relative;
  padding: 6px;
  outline: 1px solid v-bind(color);
  border-radius: 4px;

  .emoji {
    width: 20px;
    position: absolute;
    bottom: 10px;
    left: 6px;
    cursor: pointer;
  }
  ::v-deep(.el-textarea__inner) {
    resize: none;
    box-shadow: none;
  }
  ::v-deep(.el-input__count) {
    bottom: -25px;
    right: 80px;
  }

  .btns {
    display: flex;
    flex-direction: row-reverse;
  }
}

.action-bar {
  width: 100%;
  height: 56px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  position: fixed;
  bottom: 0;
  left: 0;
  background-color: #fff;

  .action-item {
    width: 32px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #ffffff;
    border-radius: 50%;
    border: 1px solid #ecf0f1;
  }
}

.related-projects {
  width: 100%;
  margin-top: 18px;
  margin-bottom: 18px;
  background: #ffffff;
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.25);
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #efefef;
  padding: 15px;
  box-sizing: border-box;
  font-size: 14px;
  color: #000000;

  .ranking-list__content {
    list-style: none;
    margin: 0;
    padding: 0;
    margin-top: 6px;
  }

  .ranking-list__item {
    display: flex;
    align-items: center;
    padding: 4px 0;

    &:hover {
      background-color: #f8f8f8;
    }
  }

  .ranking-list__rank {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    flex-shrink: 0;
    font-size: 14px;
    color: #666;
  }

  .ranking-list__rank-icon {
    width: 26px;
    height: 26px;
  }

  .ranking-list__item-content {
    flex: 1;
    font-size: 14px;
    color: #525252;
    line-height: 1.5;
    text-decoration: none;
    transition: color 0.3s ease;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &:hover {
      color: #409bff;
      text-decoration: underline;
    }
  }
}

.meta-favorite {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 14px;
  font-weight: normal;
  color: #646464;
  height: 100%;
  width: fit-content;
  cursor: pointer;
  .meta-favorite-icon {
    width: 18px;
    height: 18px;
    margin-right: 4px;
  }
  .meta-favorite-text {
    position: relative;
    top: 1px;
  }
}
</style>
