"""
逻辑树执行器

使用执行框架来执行逻辑树描述器和文档生成描述器集合
"""

import logging
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

from ...execution_framework.execution_engine import ExecutionEngine
from ...execution_framework.models.context import ExecutionContext, ExecutionContextBuilder
from ...execution_framework.base.descriptor import BaseDescriptor
from ..descriptors.logic_tree_descriptor import LogicTreeDescriptor
from ..descriptors.descriptor_factory import DescriptorFactory
from ..models.logic_tree import LogicTree, LogicNode, NodeType
from ..models.project_context import ProjectContext
from ..analyzers.project_analyzer import ProjectAnalyzer
from .logic_tree_manager import LogicTreeManager

logger = logging.getLogger(__name__)


class LogicTreeExecutor:
    """逻辑树执行器"""
    
    def __init__(
        self,
        execution_engine: ExecutionEngine,
        logic_tree_manager: LogicTreeManager,
        project_analyzer: Optional[ProjectAnalyzer] = None
    ):
        """
        初始化逻辑树执行器

        Args:
            execution_engine: 执行引擎
            logic_tree_manager: 逻辑树管理器
            project_analyzer: 项目分析器，如果为None则创建新实例
        """
        self.execution_engine = execution_engine
        self.logic_tree_manager = logic_tree_manager
        self.project_analyzer = project_analyzer or ProjectAnalyzer()
        self.descriptor_factory = DescriptorFactory()

        # 注册逻辑树描述器
        self.logic_tree_descriptor = LogicTreeDescriptor()
        self.execution_engine.register_descriptor(self.logic_tree_descriptor)

        # 注册文档生成描述器
        self._register_generation_descriptors()

        # 在构造阶段实例化 ExecutionContext，后续阶段仅设置变量与属性
        self.context: ExecutionContext = ExecutionContextBuilder.create_basic_context(
            process_id=f"readme_pipeline_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}",
            process_name="README文档流水线",
            process_type="readme_generation",
            workspace_path=None,
        )
    
    def _register_generation_descriptors(self):
        """注册文档生成描述器"""
        for name, descriptor in self.descriptor_factory.get_all_descriptors().items():
            self.execution_engine.register_descriptor(descriptor)
            logger.info(f"注册文档生成描述器: {name}")
    
    async def analyze_project(
        self,
        project_path: str,
        project_name: Optional[str] = None,
        project_url: Optional[str] = None
    ) -> Optional[ProjectContext]:
        """
        分析项目并获取项目上下文

        Args:
            project_path: 项目路径
            project_name: 项目名称
            project_url: 项目URL

        Returns:
            Optional[ProjectContext]: 项目上下文
        """
        try:
            # 确保项目分析器已初始化
            if not self.project_analyzer.is_initialized:
                success = await self.project_analyzer.initialize()
                if not success:
                    logger.error("项目分析器初始化失败")
                    return None

            # 执行项目分析
            project_context = await self.project_analyzer.analyze_project(
                project_path=project_path,
                project_name=project_name,
                project_url=project_url
            )

            if not project_context:
                logger.error("项目分析返回空结果")
                return None

            logger.info(f"项目分析完成: {project_context.project_name}")

            # 保存项目上下文到storage
            save_success = await self.logic_tree_manager.save_project_context(project_context)
            if not save_success:
                logger.error(f"保存项目上下文失败: {project_context.project_id}")
                # 注意：这里不返回None，因为分析已经成功，只是保存失败
                # 调用者仍然可以使用返回的项目上下文
            else:
                logger.info(f"项目上下文保存成功: {project_context.project_id}")

            return project_context

        except Exception as e:
            logger.error(f"项目分析失败: {e}")
            return None

    async def execute_logic_tree_planning(
        self,
        project_context: ProjectContext,
        model_name: str = "default",
        provider_name: Optional[str] = None,
        custom_prompt: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        执行逻辑树规划

        Args:
            project_context: 项目上下文
            model_name: 使用的模型名称
            provider_name: 模型提供商名称，如果为None则使用默认提供商
            custom_prompt: 自定义提示词

        Returns:
            Optional[Dict[str, Any]]: 规划结果
        """
        try:
            # 复用构造阶段创建的上下文，仅设置属性与变量
            self.context.workspace_path = project_context.project_path
            self.context.set_variable("custom_prompt", custom_prompt or "")

            # 设置项目相关变量
            self.context.set_variable("project_name", project_context.project_name)
            self.context.set_variable("project_path", project_context.project_path)
            self.context.set_variable("project_type", project_context.project_type)
            self.context.set_variable("programming_languages", ", ".join(project_context.programming_languages))
            self.context.set_variable("frameworks", ", ".join(project_context.frameworks))
            self.context.set_variable("directory_structure", project_context.project_structure.structure_string)
            self.context.set_variable("codebase_analysis", None)
            self.context.set_variable("existing_readme", project_context.existing_readme or "")
            
            logger.info(f"开始执行逻辑树规划: {project_context.project_name}")
            
            # 执行逻辑树描述器
            result = await self.execution_engine.execute_single_descriptor(
                descriptor_name=self.logic_tree_descriptor.name,
                context=self.context,
                model_name=model_name,
                provider_name=provider_name
            )
            
            if result.get("success"):
                logger.info("逻辑树规划执行成功")
                return result
            else:
                logger.error(f"逻辑树规划执行失败: {result.get('error')}")
                return None
                
        except Exception as e:
            logger.error(f"执行逻辑树规划失败: {e}")
            return None
    
    async def parse_planning_result(self, planning_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        解析规划结果
        
        Args:
            planning_result: 规划结果
            
        Returns:
            Optional[Dict[str, Any]]: 解析后的规划数据
        """
        try:
            # 获取输出内容
            output = planning_result.get("result", {}).get("output", "")
            if not output:
                logger.error("规划结果为空")
                return None
            
            # 尝试解析JSON
            try:
                # 如果输出包含代码块，提取JSON部分
                if "```json" in output:
                    start = output.find("```json") + 7
                    end = output.find("```", start)
                    json_str = output[start:end].strip()
                elif "```" in output:
                    start = output.find("```") + 3
                    end = output.find("```", start)
                    json_str = output[start:end].strip()
                else:
                    json_str = output.strip()
                
                planning_data = json.loads(json_str)
                logger.info("成功解析规划结果")
                return planning_data
                
            except json.JSONDecodeError as e:
                logger.error(f"解析JSON失败: {e}")
                logger.debug(f"原始输出: {output}")
                return None
                
        except Exception as e:
            logger.error(f"解析规划结果失败: {e}")
            return None
    
    async def create_logic_tree_from_planning(
        self,
        planning_data: Dict[str, Any],
        project_id: str,
        tree_name: str = "README逻辑树"
    ) -> Optional[LogicTree]:
        """
        根据规划数据创建逻辑树
        
        Args:
            planning_data: 规划数据
            project_id: 项目ID
            tree_name: 逻辑树名称
            
        Returns:
            Optional[LogicTree]: 创建的逻辑树
        """
        try:
            # 创建逻辑树
            tree = await self.logic_tree_manager.create_logic_tree(
                name=tree_name,
                project_id=project_id,
                description="基于AI规划生成的README文档结构"
            )
            
            if not tree:
                logger.error("创建逻辑树失败")
                return None
            
            # 获取选中的描述器
            selected_descriptors = planning_data.get("selected_descriptors", [])
            execution_plan = planning_data.get("execution_plan", {})
            order = execution_plan.get("order", [])
            
            # 按顺序添加节点
            for i, descriptor_name in enumerate(order):
                # 查找描述器信息
                descriptor_info = None
                for desc in selected_descriptors:
                    if desc.get("name") == descriptor_name:
                        descriptor_info = desc
                        break
                
                if not descriptor_info:
                    logger.warning(f"未找到描述器信息: {descriptor_name}")
                    continue
                
                # 创建节点
                node = LogicNode(
                    name=descriptor_name,
                    node_type=self._get_node_type_from_descriptor(descriptor_name),
                    description=descriptor_info.get("reason", ""),
                    order=i + 1,
                    is_enabled=True,
                    is_required=descriptor_info.get("priority", 5) >= 7
                )
                
                # 添加到逻辑树
                success = await self.logic_tree_manager.add_node(
                    tree_id=tree.id,
                    node=node,
                    parent_id=tree.root_node_id
                )
                
                if success:
                    logger.info(f"添加节点成功: {descriptor_name}")
                else:
                    logger.error(f"添加节点失败: {descriptor_name}")
            
            logger.info(f"根据规划创建逻辑树成功: {tree.id}")
            return tree
            
        except Exception as e:
            logger.error(f"根据规划创建逻辑树失败: {e}")
            return None
    
    def _get_node_type_from_descriptor(self, descriptor_name: str) -> NodeType:
        """
        根据描述器名称获取节点类型
        
        Args:
            descriptor_name: 描述器名称
            
        Returns:
            NodeType: 节点类型
        """
        type_mapping = {
            "introduction": NodeType.INTRODUCTION,
            "features": NodeType.FEATURES,
            "tech_stack": NodeType.TECH_STACK,
            "architecture": NodeType.ARCHITECTURE,
            "modules": NodeType.MODULES,
            "dependencies": NodeType.DEPENDENCIES,
            "installation": NodeType.INSTALLATION,
            "usage": NodeType.USAGE,
            "api": NodeType.API,
            "deployment": NodeType.DEPLOYMENT,
            "contribution": NodeType.CONTRIBUTION,
            "license": NodeType.LICENSE
        }
        
        return type_mapping.get(descriptor_name, NodeType.CUSTOM)

    async def execute_document_generation(
        self,
        tree: LogicTree,
        project_context: ProjectContext,
        model_name: str = "default",
        provider_name: Optional[str] = None,
        custom_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        执行文档生成

        Args:
            tree: 逻辑树
            project_context: 项目上下文
            model_name: 使用的模型名称
            provider_name: 模型提供商名称，如果为None则使用默认提供商
            custom_prompt: 自定义提示词

        Returns:
            Dict[str, Any]: 生成结果
        """
        try:
            # 获取启用的节点（排除根节点）
            enabled_nodes = [
                node for node in tree.nodes.values()
                if node.is_enabled and node.node_type != NodeType.ROOT
            ]

            # 按顺序排序
            enabled_nodes.sort(key=lambda n: n.order)

            logger.info(f"开始执行文档生成，共 {len(enabled_nodes)} 个节点")

            # 获取对应的描述器
            descriptors_to_execute = []
            for node in enabled_nodes:
                descriptor = self.descriptor_factory.get_descriptor(node.name)
                if descriptor:
                    descriptors_to_execute.append(descriptor)
                else:
                    logger.warning(f"未找到描述器: {node.name}")

            if not descriptors_to_execute:
                logger.error("没有可执行的描述器")
                return {"success": False, "error": "没有可执行的描述器"}

            # 复用上下文，设置运行阶段所需变量
            self.context.workspace_path = project_context.project_path
            if custom_prompt is not None:
                self.context.set_variable("custom_prompt", custom_prompt)

            # 设置项目相关变量
            self.context.set_variable("project_name", project_context.project_name)
            self.context.set_variable("project_path", project_context.project_path)
            self.context.set_variable("project_type", project_context.project_type)
            self.context.set_variable("programming_languages", ", ".join(project_context.programming_languages))
            self.context.set_variable("frameworks", ", ".join(project_context.frameworks))
            self.context.set_variable("codebase_analysis", None)
            self.context.set_variable("project_config", None)

            # 执行工作流
            result = await self.execution_engine.execute_workflow(
                descriptors=descriptors_to_execute,
                context=self.context,
                dependencies=None,  # 暂时不设置依赖关系
                max_parallel=1,  # 顺序执行
                fail_fast=False,
                model_name=model_name,
                provider_name=provider_name
            )
            
            logger.info("文档生成执行完成")
            return result

        except Exception as e:
            logger.error(f"执行文档生成失败: {e}")
            return {"success": False, "error": str(e)}

    async def save_generation_results(
        self,
        tree: LogicTree,
        generation_results: Dict[str, Any],
        created_by: str = "system"
    ) -> bool:
        """
        保存生成结果到逻辑树节点内容

        Args:
            tree: 逻辑树
            generation_results: 生成结果
            created_by: 创建者

        Returns:
            bool: 是否保存成功
        """
        try:
            if not generation_results.get("success"):
                logger.error("生成结果不成功，跳过保存")
                return False

            step_results = generation_results.get("results", {})

            for step_name, step_result in step_results.items():
                # 查找对应的节点
                node = None
                for n in tree.nodes.values():
                    if n.name == step_name:
                        node = n
                        break

                if not node:
                    logger.warning(f"未找到对应节点: {step_name}")
                    continue

                if not step_result.get("descriptor_result", {}).get("success"):
                    logger.warning(f"步骤 {step_name} 执行失败，跳过保存")
                    continue

                # 获取生成的内容
                content = step_result.get("descriptor_result", {}).get("result", {}).get("output", "")
                if not content:
                    logger.warning(f"步骤 {step_name} 没有输出内容")
                    continue

                # 检查是否已有内容
                existing_content = await self.logic_tree_manager.get_node_content(node.id)

                if existing_content:
                    # 更新现有内容（创建新版本）
                    version_id = await self.logic_tree_manager.update_node_content(
                        node_id=node.id,
                        content=content,
                        prompt_used=step_result.get("prompt_used", ""),
                        model_name=step_result.get("model_name", ""),
                        provider_name=step_result.get("provider_name", ""),
                        generation_config=step_result.get("generation_config", {}),
                        created_by=created_by
                    )

                    if version_id:
                        logger.info(f"更新节点内容成功: {step_name}, 版本: {version_id}")
                    else:
                        logger.error(f"更新节点内容失败: {step_name}")
                else:
                    # 创建新内容
                    node_content = await self.logic_tree_manager.create_node_content(
                        tree_id=tree.id,
                        node_id=node.id,
                        content=content,
                        prompt_used=step_result.get("prompt_used", ""),
                        model_name=step_result.get("model_name", ""),
                        provider_name=step_result.get("provider_name", ""),
                        generation_config=step_result.get("generation_config", {}),
                        created_by=created_by
                    )

                    if node_content:
                        logger.info(f"创建节点内容成功: {step_name}")
                    else:
                        logger.error(f"创建节点内容失败: {step_name}")

            return True

        except Exception as e:
            logger.error(f"保存生成结果失败: {e}")
            return False

    async def generate_logic_tree(
        self,
        project_path: str,
        project_name: Optional[str] = None,
        project_url: Optional[str] = None,
        model_name: str = "default",
        provider_name: Optional[str] = None,
        custom_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        流程1：生成逻辑树

        Args:
            project_path: 项目路径
            project_name: 项目名称
            project_url: 项目URL
            model_name: 使用的模型名称
            provider_name: 模型提供商名称，如果为None则使用默认提供商
            custom_prompt: 自定义提示词

        Returns:
            Dict[str, Any]: 生成结果，包含tree_id和project_context
        """
        try:
            logger.info(f"开始生成逻辑树: {project_path}")

            # 分析项目
            project_context = await self.analyze_project(
                project_path=project_path,
                project_name=project_name,
                project_url=project_url
            )

            if not project_context:
                return {
                    "success": False,
                    "error": "项目分析失败"
                }

            # 执行逻辑树规划
            planning_result = await self.execute_logic_tree_planning(
                project_context=project_context,
                model_name=model_name,
                provider_name=provider_name,
                custom_prompt=custom_prompt
            )

            if not planning_result:
                return {
                    "success": False,
                    "error": "逻辑树规划失败"
                }

            # 解析规划结果
            planning_data = await self.parse_planning_result(planning_result)
            if not planning_data:
                return {
                    "success": False,
                    "error": "解析规划结果失败",
                    "planning_result": planning_result
                }

            # 创建逻辑树
            tree = await self.create_logic_tree_from_planning(
                planning_data=planning_data,
                project_id=project_context.project_id,
                tree_name=f"{project_context.project_name} README"
            )

            if not tree:
                return {
                    "success": False,
                    "error": "创建逻辑树失败",
                    "planning_data": planning_data
                }

            logger.info(f"逻辑树生成成功: {tree.id}")
            return {
                "success": True,
                "tree_id": tree.id,
                "project_context": project_context,
                "planning_data": planning_data,
                "tree": tree
            }

        except Exception as e:
            logger.error(f"生成逻辑树失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def generate_all_content(
        self,
        tree_id: str,
        model_name: str = "default",
        provider_name: Optional[str] = None,
        custom_prompt: Optional[str] = None,
        created_by: str = "system"
    ) -> Dict[str, Any]:
        """
        流程2：生成完整的逻辑树节点内容

        Args:
            tree_id: 逻辑树ID
            model_name: 使用的模型名称
            provider_name: 模型提供商名称，如果为None则使用默认提供商
            custom_prompt: 自定义提示词
            created_by: 创建者

        Returns:
            Dict[str, Any]: 生成结果
        """
        try:
            logger.info(f"开始生成完整逻辑树内容: {tree_id}")

            # 获取逻辑树
            tree = await self.logic_tree_manager.get_logic_tree(tree_id)
            if not tree:
                return {
                    "success": False,
                    "error": f"逻辑树不存在: {tree_id}"
                }

            # 从storage获取项目上下文
            project_context = await self.logic_tree_manager.get_project_context(tree.project_id)

            if not project_context:
                logger.error(f"项目上下文不存在: {tree.project_id}")
                return {
                    "success": False,
                    "error": f"项目上下文不存在: {tree.project_id}"
                }

            logger.info(f"成功获取项目上下文: {tree.project_id}")

            # 执行文档生成
            generation_results = await self.execute_document_generation(
                tree=tree,
                project_context=project_context,
                model_name=model_name,
                provider_name=provider_name,
                custom_prompt=custom_prompt
            )

            if not generation_results.get("success"):
                return {
                    "success": False,
                    "error": "文档生成失败",
                    "generation_results": generation_results
                }

            # 保存生成结果
            save_success = await self.save_generation_results(
                tree=tree,
                generation_results=generation_results,
                created_by=created_by
            )

            logger.info(f"完整逻辑树内容生成完成: {tree_id}")
            return {
                "success": True,
                "tree_id": tree_id,
                "generation_results": generation_results,
                "save_success": save_success
            }

        except Exception as e:
            logger.error(f"生成完整逻辑树内容失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def generate_node(
        self,
        tree_id: str,
        node_id: str,
        model_name: str = "default",
        provider_name: Optional[str] = None,
        custom_prompt: Optional[str] = None,
        created_by: str = "system"
    ) -> Dict[str, Any]:
        """
        流程3：生成单个逻辑树节点内容

        Args:
            tree_id: 逻辑树ID
            node_id: 节点ID
            model_name: 使用的模型名称
            provider_name: 模型提供商名称，如果为None则使用默认提供商
            custom_prompt: 自定义提示词
            created_by: 创建者

        Returns:
            Dict[str, Any]: 生成结果
        """
        try:
            logger.info(f"开始生成单个节点内容: {tree_id}/{node_id}")

            # 获取逻辑树和节点
            tree = await self.logic_tree_manager.get_logic_tree(tree_id)
            if not tree:
                return {
                    "success": False,
                    "error": f"逻辑树不存在: {tree_id}"
                }

            node = tree.nodes.get(node_id)
            if not node:
                return {
                    "success": False,
                    "error": f"节点不存在: {node_id}"
                }

            # 获取对应的描述器
            descriptor = self.descriptor_factory.get_descriptor(node.name)
            if not descriptor:
                return {
                    "success": False,
                    "error": f"未找到描述器: {node.name}"
                }

            # 复用上下文（简化版本），仅设置必要变量
            # 注：workspace_path 需要外部在调用前设置或由其他组件提供
            self.context.set_variable("project_name", tree.name.replace(" README", ""))
            self.context.set_variable("project_path", self.context.workspace_path)
            self.context.set_variable("node_name", node.name)
            self.context.set_variable("node_description", node.description)
            self.context.set_variable("custom_prompt", custom_prompt or "")

            # 执行单个描述器
            result = await self.execution_engine.execute_single_descriptor(
                descriptor_name=descriptor.name,
                context=self.context,
                model_name=model_name,
                provider_name=provider_name
            )

            if not result.get("success"):
                return {
                    "success": False,
                    "error": f"节点内容生成失败: {result.get('error')}",
                    "execution_result": result
                }

            # 获取生成的内容
            content = result.get("result", {}).get("output", "")
            if not content:
                return {
                    "success": False,
                    "error": "生成的内容为空"
                }

            # 检查是否已有内容
            existing_content = await self.logic_tree_manager.get_node_content(node_id)

            if existing_content:
                # 更新现有内容（创建新版本）
                version_id = await self.logic_tree_manager.update_node_content(
                    node_id=node_id,
                    content=content,
                    prompt_used=custom_prompt or "",
                    model_name=model_name,
                    provider_name=provider_name,
                    generation_config=result.get("generation_config", {}),
                    created_by=created_by
                )

                if not version_id:
                    return {
                        "success": False,
                        "error": "更新节点内容失败"
                    }

                logger.info(f"节点内容更新成功: {node.name}, 版本: {version_id}")
                return {
                    "success": True,
                    "node_content": existing_content,
                    "action": "updated"
                }
            else:
                # 创建新内容
                node_content = await self.logic_tree_manager.create_node_content(
                    tree_id=tree_id,
                    node_id=node_id,
                    content=content,
                    prompt_used=custom_prompt or "",
                    model_name=model_name,
                    provider_name=provider_name,
                    generation_config=result.get("generation_config", {}),
                    created_by=created_by
                )

                if not node_content:
                    return {
                        "success": False,
                        "error": "创建节点内容失败"
                    }

                logger.info(f"节点内容创建成功: {node.name}")
                return {
                    "success": True,
                    "node_content": node_content,
                    "action": "created"
                }

        except Exception as e:
            logger.error(f"生成单个节点内容失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def regenerate_node(
        self,
        tree_id: str,
        node_id: str,
        model_name: str = "default",
        provider_name: Optional[str] = None,
        custom_prompt: Optional[str] = None,
        created_by: str = "system"
    ) -> Dict[str, Any]:
        """
        流程4：重新生成单个逻辑树节点内容

        Args:
            tree_id: 逻辑树ID
            node_id: 节点ID
            model_name: 使用的模型名称
            provider_name: 模型提供商名称，如果为None则使用默认提供商
            custom_prompt: 自定义提示词
            created_by: 创建者

        Returns:
            Dict[str, Any]: 生成结果
        """
        try:
            logger.info(f"开始重新生成单个节点内容: {tree_id}/{node_id}")

            # 检查节点是否已有内容
            existing_content = await self.logic_tree_manager.get_node_content(node_id)
            if not existing_content:
                return {
                    "success": False,
                    "error": "节点没有现有内容，请使用生成功能而不是重新生成功能"
                }

            # 调用生成单个节点内容的方法（会自动创建新版本）
            result = await self.generate_node(
                tree_id=tree_id,
                node_id=node_id,
                model_name=model_name,
                provider_name=provider_name,
                custom_prompt=custom_prompt,
                created_by=created_by
            )

            if result.get("success"):
                # 修改返回结果中的action标识
                result["action"] = "regenerated"
                logger.info(f"节点内容重新生成成功: {node_id}")

            return result

        except Exception as e:
            logger.error(f"重新生成单个节点内容失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_generated_readme_content(self, tree_id: str) -> Optional[str]:
        """
        获取生成的README内容

        Args:
            tree_id: 逻辑树ID

        Returns:
            Optional[str]: README内容
        """
        try:
            tree = await self.logic_tree_manager.get_logic_tree(tree_id)
            if not tree:
                logger.error(f"逻辑树不存在: {tree_id}")
                return None

            # 获取所有节点内容
            contents = await self.logic_tree_manager.get_tree_contents(tree_id)
            if not contents:
                logger.warning(f"逻辑树 {tree_id} 没有内容")
                return None

            # 按节点顺序组织内容
            content_parts = []

            # 获取启用的节点并排序
            enabled_nodes = [
                node for node in tree.nodes.values()
                if node.is_enabled and node.node_type != NodeType.ROOT
            ]
            enabled_nodes.sort(key=lambda n: n.order)

            for node in enabled_nodes:
                # 查找对应的内容
                node_content = None
                for content in contents:
                    if content.node_id == node.id:
                        node_content = content
                        break

                if node_content:
                    current_version = node_content.get_current_version()
                    if current_version:
                        content_parts.append(current_version.content)

            if not content_parts:
                logger.warning(f"逻辑树 {tree_id} 没有有效内容")
                return None

            # 组合所有内容
            readme_content = "\n\n".join(content_parts)

            logger.info(f"成功获取README内容，总长度: {len(readme_content)}")
            return readme_content

        except Exception as e:
            logger.error(f"获取README内容失败: {e}")
            return None