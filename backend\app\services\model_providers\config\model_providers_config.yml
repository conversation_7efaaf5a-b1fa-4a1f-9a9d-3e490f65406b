# 模型提供商配置文件

# 默认提供商
default_provider: "google"

# 当前模型
current_model: "google/gemma-2-9b-it:free"

# 提供商配置
providers:
  # OpenAI提供商
  openai:
    name: "openai"
    provider: "openai"
    api_key: "${OPENAI_API_KEY}"  # 从环境变量读取
    api_base: "https://api.openai.com/v1"  # 可选，自定义API地址
    timeout: 30
    max_retries: 3
    retry_delay: 1.0
    enabled: true
    description: "OpenAI官方API"
    extra_headers:
      "User-Agent": "gugu-apex/1.0"
    models:
      - name: "gpt-4o"
        provider: "openai"
        temperature: 0.7
        top_p: 1.0
        extra_params:
          frequency_penalty: 0.0
          presence_penalty: 0.0
      - name: "gpt-4o-mini"
        provider: "openai"
        temperature: 0.7
        top_p: 1.0
      - name: "gpt-3.5-turbo"
        provider: "openai"
        temperature: 0.7
        top_p: 1.0

  # Google提供商
  google:
    name: "google"
    provider: "google"
    api_key: "AIzaSyAK5PCLBMCk2YnYgRMk7FQzVEVxHFi_Ndw"  # 从环境变量读取
    api_base: "https://generativelanguage.googleapis.com/v1beta"
    timeout: 30
    max_retries: 3
    retry_delay: 1.0
    enabled: false  # 默认禁用
    description: "Google Gemini API"
    models:
      - name: "gemini-2.5-flash"
        provider: "google"
        temperature: 0.7
        top_p: 1.0
        top_k: 40
      - name: "gemini-1.5-pro"
        provider: "google"
        temperature: 0.7
        top_p: 1.0
        top_k: 40

  # OpenRouter提供商
  openrouter:
    name: "openrouter"
    provider: "openrouter"
    api_key: "${OPENROUTER_API_KEY}"  # 从环境变量读取
    api_base: "https://openrouter.ai/api/v1"
    timeout: 60
    max_retries: 3
    retry_delay: 1.0
    enabled: true
    description: "OpenRouter聚合API"
    default_model: "google/gemma-2-9b-it:free"
    extra_headers:
      "HTTP-Referer": "https://gugu-apex.com"
      "X-Title": "Gugu Apex"
    models:
      - name: "google/gemma-2-9b-it:free"
        provider: "openrouter"
        temperature: 0.7
        top_p: 1.0
        extra_params:
          repetition_penalty: 1.1
      - name: "meta-llama/llama-3.1-8b-instruct:free"
        provider: "openrouter"
        temperature: 0.7
        top_p: 1.0
      - name: "anthropic/claude-3.5-sonnet"
        provider: "openrouter"
        temperature: 0.7
        top_p: 1.0
      - name: "qwen/qwen-2.5-72b-instruct"
        provider: "openrouter"
        temperature: 0.7
        top_p: 1.0

  # Ollama本地提供商
  ollama:
    name: "ollama"
    provider: "ollama"
    api_base: "http://localhost:11434"
    timeout: 120  # 本地模型可能需要更长时间
    max_retries: 2
    retry_delay: 2.0
    enabled: false  # 默认禁用，需要手动启用
    description: "Ollama本地模型服务"
    models:
      - name: "llama3.1:8b"
        provider: "ollama"
        temperature: 0.7
        top_p: 1.0
        top_k: 40
        extra_params:
          num_predict: 8192
          num_ctx: 131072
      - name: "qwen2.5:7b"
        provider: "ollama"
        temperature: 0.7
        top_p: 1.0
        top_k: 40
      - name: "codellama:7b"
        provider: "ollama"
        temperature: 0.1  # 代码生成使用较低温度
        top_p: 0.9
      - name: "llava:7b"
        provider: "ollama"
        temperature: 0.7
        top_p: 1.0
        extra_params:
          num_predict: 4096

  # 国内提供商配置
  # 百度文心一言
  baidu:
    name: "baidu"
    provider: "baidu"
    api_key: "${BAIDU_API_KEY}"
    secret_key: "${BAIDU_SECRET_KEY}"
    api_base: "https://aip.baidubce.com"
    timeout: 30
    max_retries: 3
    retry_delay: 1.0
    enabled: false
    description: "百度文心一言API"
    models:
      - name: "ernie-4.0-8k"
        provider: "baidu"
        temperature: 0.7
        top_p: 1.0
      - name: "ernie-3.5-8k"
        provider: "baidu"
        temperature: 0.7
        top_p: 1.0
      - name: "ernie-turbo-8k"
        provider: "baidu"
        temperature: 0.7
        top_p: 1.0

  # 阿里通义千问
  alibaba:
    name: "alibaba"
    provider: "alibaba"
    api_key: "${ALIBABA_API_KEY}"
    api_base: "https://dashscope.aliyuncs.com"
    timeout: 30
    max_retries: 3
    retry_delay: 1.0
    enabled: false
    description: "阿里通义千问API"
    models:
      - name: "qwen-max"
        provider: "alibaba"
        temperature: 0.7
        top_p: 1.0
      - name: "qwen-plus"
        provider: "alibaba"
        temperature: 0.7
        top_p: 1.0
      - name: "qwen-turbo"
        provider: "alibaba"
        temperature: 0.7
        top_p: 1.0
      - name: "qwen-long"
        provider: "alibaba"
        temperature: 0.7
        top_p: 1.0

  # 智谱GLM
  zhipu:
    name: "zhipu"
    provider: "zhipu"
    api_key: "${ZHIPU_API_KEY}"
    api_base: "https://open.bigmodel.cn"
    timeout: 30
    max_retries: 3
    retry_delay: 1.0
    enabled: false
    description: "智谱GLM API"
    models:
      - name: "glm-4"
        provider: "zhipu"
        temperature: 0.7
        top_p: 1.0
      - name: "glm-4-air"
        provider: "zhipu"
        temperature: 0.7
        top_p: 1.0
      - name: "glm-4-flash"
        provider: "zhipu"
        temperature: 0.7
        top_p: 1.0
      - name: "glm-4v"
        provider: "zhipu"
        temperature: 0.7
        top_p: 1.0
      - name: "codegeex-4"
        provider: "zhipu"
        temperature: 0.1  # 代码生成使用较低温度
        top_p: 0.9

  # 月之暗面Kimi
  moonshot:
    name: "moonshot"
    provider: "moonshot"
    api_key: "${MOONSHOT_API_KEY}"
    api_base: "https://api.moonshot.cn"
    timeout: 30
    max_retries: 3
    retry_delay: 1.0
    enabled: false
    description: "月之暗面Kimi API"
    models:
      - name: "moonshot-v1-8k"
        provider: "moonshot"
        temperature: 0.7
        top_p: 1.0
      - name: "moonshot-v1-32k"
        provider: "moonshot"
        temperature: 0.7
        top_p: 1.0
      - name: "moonshot-v1-128k"
        provider: "moonshot"
        temperature: 0.7
        top_p: 1.0

  # DeepSeek
  deepseek:
    name: "deepseek"
    provider: "deepseek"
    api_key: "${DEEPSEEK_API_KEY}"
    api_base: "https://api.deepseek.com"
    timeout: 30
    max_retries: 3
    retry_delay: 1.0
    enabled: false
    description: "DeepSeek API"
    models:
      - name: "deepseek-chat"
        provider: "deepseek"
        temperature: 0.7
        top_p: 1.0
      - name: "deepseek-coder"
        provider: "deepseek"
        temperature: 0.1  # 代码生成使用较低温度
        top_p: 0.9
      - name: "deepseek-v2"
        provider: "deepseek"
        temperature: 0.7
        top_p: 1.0

# 全局配置
global_config:
  # 模型切换策略
  auto_fallback: true  # 是否自动切换到备用模型
  fallback_models:
    - "google/gemma-2-9b-it:free"
    - "meta-llama/llama-3.1-8b-instruct:free"
  
  # 缓存配置
  model_cache_ttl: 300  # 模型列表缓存时间（秒）
  
  # 监控配置
  health_check_interval: 60  # 健康检查间隔（秒）
  
  # 日志配置
  log_requests: false  # 是否记录请求日志
  log_responses: false  # 是否记录响应日志
