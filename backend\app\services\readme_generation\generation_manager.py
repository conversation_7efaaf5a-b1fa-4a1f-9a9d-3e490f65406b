"""
README生成完整管理器

基于核心组件提供统一的README生成管理接口
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from ..execution_framework.execution_engine import ExecutionEngine
from ..codebase.manager import CodeBaseManager
from .core.version_manager import VersionManager
from .core.logic_tree_manager import LogicTreeManager
from .core.logic_tree_executor import LogicTreeExecutor
from .analyzers.project_analyzer import ProjectAnalyzer
from .storage.base import BaseStorage
from .storage.memory import MemoryStorage
from .models.logic_tree import LogicTree, LogicNode, NodeType
from .models.content import NodeContent
from .models.project_context import ProjectContext

logger = logging.getLogger(__name__)


class GenerationManager:
    """README生成完整管理器"""
    
    def __init__(
        self,
        storage: Optional[BaseStorage] = None,
        codebase_manager: Optional[CodeBaseManager] = None,
        execution_engine: Optional[ExecutionEngine] = None
    ):
        """
        初始化生成管理器
        
        Args:
            storage: 存储实例
            codebase_manager: 代码库管理器
            execution_engine: 执行引擎
        """
        self.storage = storage or MemoryStorage()
        self.codebase_manager = codebase_manager
        self.execution_engine = execution_engine or ExecutionEngine(codebase_manager=self.codebase_manager)
        
        # 核心组件
        self.version_manager = VersionManager(self.storage)
        self.logic_tree_manager = LogicTreeManager(self.storage)
        self.project_analyzer = ProjectAnalyzer(self.codebase_manager)
        self.logic_tree_executor = LogicTreeExecutor(
            execution_engine=self.execution_engine,
            logic_tree_manager=self.logic_tree_manager,
            project_analyzer=self.project_analyzer
        )
        
        self.is_initialized = False
    
    async def initialize(self) -> bool:
        """
        初始化管理器
        
        Returns:
            bool: 是否初始化成功
        """
        try:
            # 初始化存储
            if not await self.storage.initialize():
                logger.error("存储初始化失败")
                return False
            
            # 初始化项目分析器
            if not await self.project_analyzer.initialize():
                logger.error("项目分析器初始化失败")
                return False
            
            # 初始化执行引擎的描述器执行器
            if not await self.execution_engine.descriptor_executor.initialize():
                logger.error("执行引擎描述器执行器初始化失败")
                return False
            
            self.is_initialized = True
            logger.info("README生成管理器初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"README生成管理器初始化失败: {e}")
            return False
    
    # ==================== 流程1：生成逻辑树 ====================
    
    async def generate_logic_tree(
        self,
        project_path: str,
        project_name: Optional[str] = None,
        project_url: Optional[str] = None,
        model_name: str = "default",
        provider_name: Optional[str] = None,
        custom_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        流程1：生成逻辑树

        Args:
            project_path: 项目路径
            project_name: 项目名称
            project_url: 项目URL
            model_name: 使用的模型名称
            provider_name: 模型提供商名称
            custom_prompt: 自定义提示词，用于指导逻辑树规划

        Returns:
            Dict[str, Any]: 生成结果
        """
        if not self.is_initialized:
            return {"success": False, "error": "管理器未初始化"}
        
        return await self.logic_tree_executor.generate_logic_tree(
            project_path=project_path,
            project_name=project_name,
            project_url=project_url,
            model_name=model_name,
            provider_name=provider_name,
            custom_prompt=custom_prompt
        )
    
    # ==================== 流程2：生成完整内容 ====================
    
    async def generate_all_content(
        self,
        tree_id: str,
        model_name: str = "default",
        provider_name: Optional[str] = None,
        created_by: str = "system"
    ) -> Dict[str, Any]:
        """
        流程2：生成完整的逻辑树节点内容
        
        Args:
            tree_id: 逻辑树ID
            model_name: 使用的模型名称
            provider_name: 模型提供商名称
            created_by: 创建者
            
        Returns:
            Dict[str, Any]: 生成结果
        """
        if not self.is_initialized:
            return {"success": False, "error": "管理器未初始化"}
        
        return await self.logic_tree_executor.generate_all_content(
            tree_id=tree_id,
            model_name=model_name,
            provider_name=provider_name,
            created_by=created_by
        )
    
    # ==================== 流程3：生成单个节点 ====================
    
    async def generate_node(
        self,
        tree_id: str,
        node_id: str,
        model_name: str = "default",
        provider_name: Optional[str] = None,
        custom_prompt: Optional[str] = None,
        created_by: str = "system"
    ) -> Dict[str, Any]:
        """
        流程3：生成单个逻辑树节点内容
        
        Args:
            tree_id: 逻辑树ID
            node_id: 节点ID
            model_name: 使用的模型名称
            provider_name: 模型提供商名称
            custom_prompt: 自定义提示词
            created_by: 创建者
            
        Returns:
            Dict[str, Any]: 生成结果
        """
        if not self.is_initialized:
            return {"success": False, "error": "管理器未初始化"}
        
        return await self.logic_tree_executor.generate_node(
            tree_id=tree_id,
            node_id=node_id,
            model_name=model_name,
            provider_name=provider_name,
            custom_prompt=custom_prompt,
            created_by=created_by
        )
    
    # ==================== 流程4：重新生成单个节点 ====================
    
    async def regenerate_node(
        self,
        tree_id: str,
        node_id: str,
        model_name: str = "default",
        provider_name: Optional[str] = None,
        custom_prompt: Optional[str] = None,
        created_by: str = "system"
    ) -> Dict[str, Any]:
        """
        流程4：重新生成单个逻辑树节点内容
        
        Args:
            tree_id: 逻辑树ID
            node_id: 节点ID
            model_name: 使用的模型名称
            provider_name: 模型提供商名称
            custom_prompt: 自定义提示词
            created_by: 创建者
            
        Returns:
            Dict[str, Any]: 生成结果
        """
        if not self.is_initialized:
            return {"success": False, "error": "管理器未初始化"}
        
        return await self.logic_tree_executor.regenerate_node(
            tree_id=tree_id,
            node_id=node_id,
            model_name=model_name,
            provider_name=provider_name,
            custom_prompt=custom_prompt,
            created_by=created_by
        )
    
    # ==================== 逻辑树管理接口 ====================
    
    async def get_logic_tree(self, tree_id: str) -> Optional[LogicTree]:
        """获取逻辑树"""
        return await self.logic_tree_manager.get_logic_tree(tree_id)
    
    async def get_logic_trees_by_project(self, project_id: str) -> List[LogicTree]:
        """根据项目获取逻辑树列表"""
        return await self.logic_tree_manager.get_logic_trees_by_project(project_id)

    async def get_all_logic_trees(self) -> List[LogicTree]:
        """获取所有逻辑树"""
        return await self.logic_tree_manager.get_all_logic_trees()

    async def update_logic_tree(self, tree: LogicTree) -> bool:
        """更新逻辑树"""
        return await self.logic_tree_manager.update_logic_tree(tree)
    
    async def delete_logic_tree(self, tree_id: str) -> bool:
        """删除逻辑树"""
        return await self.logic_tree_manager.delete_logic_tree(tree_id)
    
    async def add_node(
        self,
        tree_id: str,
        node: LogicNode,
        parent_id: Optional[str] = None
    ) -> bool:
        """添加节点到逻辑树"""
        return await self.logic_tree_manager.add_node(tree_id, node, parent_id)
    
    async def remove_node(self, tree_id: str, node_id: str) -> bool:
        """从逻辑树移除节点"""
        return await self.logic_tree_manager.remove_node(tree_id, node_id)
    
    async def update_node(self, tree_id: str, node: LogicNode) -> bool:
        """更新节点"""
        return await self.logic_tree_manager.update_node(tree_id, node)
    
    async def reorder_nodes(
        self,
        tree_id: str,
        node_orders: Dict[str, int]
    ) -> bool:
        """重新排序节点"""
        return await self.logic_tree_manager.reorder_nodes(tree_id, node_orders)
    
    async def clone_logic_tree(
        self,
        source_tree_id: str,
        new_name: str,
        new_project_id: Optional[str] = None
    ) -> Optional[LogicTree]:
        """克隆逻辑树"""
        return await self.logic_tree_manager.clone_logic_tree(
            source_tree_id, new_name, new_project_id
        )
    
    async def get_tree_statistics(self, tree_id: str) -> Optional[Dict[str, Any]]:
        """获取逻辑树统计信息"""
        return await self.logic_tree_manager.get_tree_statistics(tree_id)

    # ==================== 节点内容管理接口 ====================

    async def get_node_content(self, node_id: str) -> Optional[NodeContent]:
        """获取节点内容"""
        return await self.logic_tree_manager.get_node_content(node_id)

    async def create_node_content(
        self,
        tree_id: str,
        node_id: str,
        content: str,
        prompt_used: str = "",
        model_name: str = "",
        provider_name: Optional[str] = None,
        generation_config: Optional[Dict[str, Any]] = None,
        created_by: str = "system"
    ) -> Optional[NodeContent]:
        """创建节点内容"""
        return await self.logic_tree_manager.create_node_content(
            tree_id=tree_id,
            node_id=node_id,
            content=content,
            prompt_used=prompt_used,
            model_name=model_name,
            provider_name=provider_name,
            generation_config=generation_config or {},
            created_by=created_by
        )

    async def update_node_content(
        self,
        node_id: str,
        content: str,
        prompt_used: str = "",
        model_name: str = "",
        provider_name: Optional[str] = None,
        generation_config: Optional[Dict[str, Any]] = None,
        created_by: str = "system"
    ) -> Optional[str]:
        """更新节点内容（创建新版本）"""
        return await self.logic_tree_manager.update_node_content(
            node_id=node_id,
            content=content,
            prompt_used=prompt_used,
            model_name=model_name,
            provider_name=provider_name,
            generation_config=generation_config or {},
            created_by=created_by
        )

    async def get_tree_contents(self, tree_id: str) -> List[NodeContent]:
        """获取逻辑树的所有节点内容"""
        return await self.logic_tree_manager.get_tree_contents(tree_id)

    # ==================== 版本管理接口 ====================

    async def get_node_version_history(self, node_id: str) -> List[Dict[str, Any]]:
        """
        获取节点的版本历史

        Args:
            node_id: 节点ID

        Returns:
            List[Dict[str, Any]]: 版本历史列表
        """
        try:
            node_content = await self.get_node_content(node_id)
            if not node_content:
                return []

            return self.version_manager.get_version_history(node_content)

        except Exception as e:
            logger.error(f"获取节点版本历史失败: {e}")
            return []

    async def switch_node_version(self, node_id: str, version_id: str) -> bool:
        """
        切换节点的当前版本

        Args:
            node_id: 节点ID
            version_id: 版本ID

        Returns:
            bool: 是否切换成功
        """
        try:
            node_content = await self.get_node_content(node_id)
            if not node_content:
                return False

            return await self.version_manager.switch_version(node_content, version_id)

        except Exception as e:
            logger.error(f"切换节点版本失败: {e}")
            return False

    async def delete_node_version(self, node_id: str, version_id: str) -> bool:
        """
        删除节点的指定版本

        Args:
            node_id: 节点ID
            version_id: 版本ID

        Returns:
            bool: 是否删除成功
        """
        try:
            node_content = await self.get_node_content(node_id)
            if not node_content:
                return False

            return await self.version_manager.delete_version(node_content, version_id)

        except Exception as e:
            logger.error(f"删除节点版本失败: {e}")
            return False

    async def set_node_version_quality_score(
        self,
        node_id: str,
        version_id: str,
        score: float,
        feedback: str = ""
    ) -> bool:
        """
        设置节点版本质量评分

        Args:
            node_id: 节点ID
            version_id: 版本ID
            score: 质量评分 (0.0-1.0)
            feedback: 反馈信息

        Returns:
            bool: 是否设置成功
        """
        try:
            node_content = await self.get_node_content(node_id)
            if not node_content:
                return False

            return await self.version_manager.set_version_quality_score(
                node_content, version_id, score, feedback
            )

        except Exception as e:
            logger.error(f"设置节点版本质量评分失败: {e}")
            return False

    async def get_node_version_statistics(self, node_id: str) -> Dict[str, Any]:
        """
        获取节点的版本统计信息

        Args:
            node_id: 节点ID

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            node_content = await self.get_node_content(node_id)
            if not node_content:
                return {}

            return self.version_manager.get_version_statistics(node_content)

        except Exception as e:
            logger.error(f"获取节点版本统计信息失败: {e}")
            return {}

    async def compare_node_versions(
        self,
        node_id: str,
        version_id1: str,
        version_id2: str
    ) -> Optional[Dict[str, Any]]:
        """
        比较节点的两个版本

        Args:
            node_id: 节点ID
            version_id1: 第一个版本ID
            version_id2: 第二个版本ID

        Returns:
            Optional[Dict[str, Any]]: 比较结果
        """
        try:
            node_content = await self.get_node_content(node_id)
            if not node_content:
                return None

            return self.version_manager.compare_versions(node_content, version_id1, version_id2)

        except Exception as e:
            logger.error(f"比较节点版本失败: {e}")
            return None

    # ==================== 项目分析和导出接口 ====================

    async def analyze_project(
        self,
        project_path: str,
        project_name: Optional[str] = None,
        project_url: Optional[str] = None
    ) -> Optional[ProjectContext]:
        """分析项目"""
        return await self.logic_tree_executor.analyze_project(
            project_path=project_path,
            project_name=project_name,
            project_url=project_url
        )

    async def get_generated_readme_content(self, tree_id: str) -> Optional[str]:
        """获取生成的README内容"""
        return await self.logic_tree_executor.get_generated_readme_content(tree_id)

    async def export_readme(
        self,
        tree_id: str,
        format_type: str = "markdown"
    ) -> Optional[str]:
        """
        导出README文档

        Args:
            tree_id: 逻辑树ID
            format_type: 导出格式（markdown, html等）

        Returns:
            Optional[str]: 导出的文档内容
        """
        try:
            content = await self.get_generated_readme_content(tree_id)
            if not content:
                return None

            # 根据格式类型处理内容
            if format_type.lower() == "markdown":
                return content
            elif format_type.lower() == "html":
                # TODO: 实现Markdown到HTML的转换
                return content
            else:
                logger.warning(f"不支持的导出格式: {format_type}")
                return content

        except Exception as e:
            logger.error(f"导出README失败: {e}")
            return None



    # ==================== 统计和监控接口 ====================

    async def get_generation_statistics(self, tree_id: str) -> Dict[str, Any]:
        """
        获取生成统计信息

        Args:
            tree_id: 逻辑树ID

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 获取逻辑树统计
            tree_stats = await self.get_tree_statistics(tree_id)
            if not tree_stats:
                return {"error": "逻辑树不存在"}

            # 获取逻辑树
            tree = await self.get_logic_tree(tree_id)
            if not tree:
                return {"error": "逻辑树不存在"}

            # 获取所有节点内容
            contents = await self.get_tree_contents(tree_id)

            # 计算完成率
            total_nodes = len([n for n in tree.nodes.values() if n.node_type != NodeType.ROOT])
            generated_nodes = len(contents)
            completion_rate = generated_nodes / total_nodes if total_nodes > 0 else 0

            # 计算总版本数
            total_versions = sum(len(content.versions) for content in contents)

            return {
                "tree_id": tree_id,
                "tree_name": tree.name,
                "total_nodes": total_nodes,
                "generated_nodes": generated_nodes,
                "completion_rate": completion_rate,
                "total_versions": total_versions,
                "created_at": tree.created_at.isoformat() if tree.created_at else None,
                "updated_at": tree.updated_at.isoformat() if tree.updated_at else None
            }

        except Exception as e:
            logger.error(f"获取生成统计信息失败: {e}")
            return {"error": str(e)}

    async def health_check(self) -> Dict[str, bool]:
        """
        健康检查

        Returns:
            Dict[str, bool]: 健康状态
        """
        health_status = {
            "manager_initialized": self.is_initialized,
            "storage_available": False,
            "project_analyzer_available": False,
            "execution_engine_available": False
        }

        try:
            # 检查存储
            health_status["storage_available"] = await self.storage.health_check()

            # 检查项目分析器
            health_status["project_analyzer_available"] = self.project_analyzer.is_initialized

            # 检查执行引擎（通过检查描述器执行器的初始化状态）
            health_status["execution_engine_available"] = getattr(
                self.execution_engine.descriptor_executor, '_initialized', False
            )

        except Exception as e:
            logger.error(f"健康检查失败: {e}")

        return health_status

    async def shutdown(self) -> None:
        """关闭管理器"""
        try:
            # 关闭项目分析器
            if self.project_analyzer:
                await self.project_analyzer.shutdown()

            # 执行引擎没有shutdown方法，跳过
            # ExecutionEngine 不需要显式关闭

            # 关闭存储
            if self.storage:
                await self.storage.shutdown()

            self.is_initialized = False
            logger.info("README生成管理器已关闭")

        except Exception as e:
            logger.error(f"关闭README生成管理器失败: {e}")


# 全局管理器实例
generation_manager = GenerationManager()
