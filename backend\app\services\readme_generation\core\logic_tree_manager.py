"""
逻辑树管理器

管理README逻辑树的创建、更新、删除和节点内容版本管理
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone

from ..models.logic_tree import LogicTree, LogicNode, NodeType
from ..models.content import NodeContent, ContentStatus
from ..models.project_context import ProjectContext
from ..storage.base import BaseStorage
from .version_manager import VersionManager

logger = logging.getLogger(__name__)


class LogicTreeManager:
    """逻辑树管理器"""
    
    def __init__(self, storage: BaseStorage):
        """
        初始化逻辑树管理器
        
        Args:
            storage: 存储实例
        """
        self.storage = storage
        self.version_manager = VersionManager(storage)
    
    async def create_logic_tree(
        self,
        name: str,
        project_id: str,
        description: str = ""
    ) -> Optional[LogicTree]:
        """
        创建新的逻辑树
        
        Args:
            name: 逻辑树名称
            project_id: 项目ID
            description: 描述
            
        Returns:
            Optional[LogicTree]: 创建的逻辑树
        """
        try:
            # 创建逻辑树
            tree = LogicTree(
                name=name,
                project_id=project_id,
                description=description
            )
            
            # 创建根节点
            root_node = LogicNode(
                name="README根节点",
                node_type=NodeType.ROOT,
                description="README文档的根节点",
                is_required=True
            )
            
            # 添加根节点到树
            tree.add_node(root_node)
            
            # 保存到存储
            success = await self.storage.save_logic_tree(tree)
            if success:
                logger.info(f"创建逻辑树成功: {tree.id}")
                return tree
            else:
                logger.error(f"保存逻辑树失败: {tree.id}")
                return None
                
        except Exception as e:
            logger.error(f"创建逻辑树失败: {e}")
            return None
    
    async def get_logic_tree(self, tree_id: str) -> Optional[LogicTree]:
        """
        获取逻辑树
        
        Args:
            tree_id: 逻辑树ID
            
        Returns:
            Optional[LogicTree]: 逻辑树
        """
        return await self.storage.get_logic_tree(tree_id)
    
    async def get_logic_trees_by_project(self, project_id: str) -> List[LogicTree]:
        """
        根据项目ID获取逻辑树列表
        
        Args:
            project_id: 项目ID
            
        Returns:
            List[LogicTree]: 逻辑树列表
        """
        return await self.storage.get_logic_trees_by_project(project_id)

    async def get_all_logic_trees(self) -> List[LogicTree]:
        """
        获取所有逻辑树

        Returns:
            List[LogicTree]: 所有逻辑树列表
        """
        return await self.storage.get_all_logic_trees()

    async def update_logic_tree(self, tree: LogicTree) -> bool:
        """
        更新逻辑树
        
        Args:
            tree: 逻辑树
            
        Returns:
            bool: 是否更新成功
        """
        try:
            tree.updated_at = datetime.now(timezone.utc)
            success = await self.storage.save_logic_tree(tree)
            if success:
                logger.info(f"更新逻辑树成功: {tree.id}")
            else:
                logger.error(f"更新逻辑树失败: {tree.id}")
            return success
        except Exception as e:
            logger.error(f"更新逻辑树失败: {e}")
            return False
    
    async def delete_logic_tree(self, tree_id: str) -> bool:
        """
        删除逻辑树
        
        Args:
            tree_id: 逻辑树ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            success = await self.storage.delete_logic_tree(tree_id)
            if success:
                logger.info(f"删除逻辑树成功: {tree_id}")
            else:
                logger.error(f"删除逻辑树失败: {tree_id}")
            return success
        except Exception as e:
            logger.error(f"删除逻辑树失败: {e}")
            return False
    
    async def add_node(
        self,
        tree_id: str,
        node: LogicNode,
        parent_id: Optional[str] = None
    ) -> bool:
        """
        向逻辑树添加节点
        
        Args:
            tree_id: 逻辑树ID
            node: 节点
            parent_id: 父节点ID
            
        Returns:
            bool: 是否添加成功
        """
        try:
            tree = await self.get_logic_tree(tree_id)
            if not tree:
                logger.error(f"逻辑树不存在: {tree_id}")
                return False
            
            # 添加节点
            tree.add_node(node, parent_id)
            
            # 保存逻辑树
            success = await self.update_logic_tree(tree)
            if success:
                logger.info(f"添加节点成功: {node.id} 到逻辑树 {tree_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"添加节点失败: {e}")
            return False
    
    async def remove_node(self, tree_id: str, node_id: str) -> bool:
        """
        从逻辑树移除节点
        
        Args:
            tree_id: 逻辑树ID
            node_id: 节点ID
            
        Returns:
            bool: 是否移除成功
        """
        try:
            tree = await self.get_logic_tree(tree_id)
            if not tree:
                logger.error(f"逻辑树不存在: {tree_id}")
                return False
            
            # 移除节点
            success = tree.remove_node(node_id)
            if not success:
                logger.error(f"节点不存在: {node_id}")
                return False
            
            # 删除节点内容
            await self.storage.delete_node_content(node_id)
            
            # 保存逻辑树
            success = await self.update_logic_tree(tree)
            if success:
                logger.info(f"移除节点成功: {node_id} 从逻辑树 {tree_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"移除节点失败: {e}")
            return False
    
    async def update_node(self, tree_id: str, node: LogicNode) -> bool:
        """
        更新节点
        
        Args:
            tree_id: 逻辑树ID
            node: 节点
            
        Returns:
            bool: 是否更新成功
        """
        try:
            tree = await self.get_logic_tree(tree_id)
            if not tree:
                logger.error(f"逻辑树不存在: {tree_id}")
                return False
            
            if node.id not in tree.nodes:
                logger.error(f"节点不存在: {node.id}")
                return False
            
            # 更新节点
            node.updated_at = datetime.now(timezone.utc)
            tree.nodes[node.id] = node
            
            # 保存逻辑树
            success = await self.update_logic_tree(tree)
            if success:
                logger.info(f"更新节点成功: {node.id}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新节点失败: {e}")
            return False
    
    async def get_node_content(self, node_id: str) -> Optional[NodeContent]:
        """
        获取节点内容
        
        Args:
            node_id: 节点ID
            
        Returns:
            Optional[NodeContent]: 节点内容
        """
        return await self.storage.get_node_content_by_node(node_id)
    
    async def create_node_content(
        self,
        tree_id: str,
        node_id: str,
        content: str,
        prompt_used: str = "",
        model_name: str = "",
        provider_name: Optional[str] = None,
        generation_config: Optional[Dict[str, Any]] = None,
        created_by: str = "system"
    ) -> Optional[NodeContent]:
        """
        创建节点内容
        
        Args:
            tree_id: 逻辑树ID
            node_id: 节点ID
            content: 内容文本
            prompt_used: 使用的提示词
            model_name: 使用的模型名称
            provider_name: 模型提供商名称
            generation_config: 生成配置
            created_by: 创建者
            
        Returns:
            Optional[NodeContent]: 创建的节点内容
        """
        try:
            # 检查节点是否存在
            tree = await self.get_logic_tree(tree_id)
            if not tree or node_id not in tree.nodes:
                logger.error(f"节点不存在: {node_id}")
                return None
            
            # 创建节点内容
            node_content = NodeContent(
                tree_id=tree_id,
                node_id=node_id
            )
            
            # 创建初始版本
            await self.version_manager.create_version(
                node_content=node_content,
                content=content,
                prompt_used=prompt_used,
                model_name=model_name,
                provider_name=provider_name,
                generation_config=generation_config or {},
                created_by=created_by
            )
            
            logger.info(f"创建节点内容成功: {node_content.id}")
            return node_content

        except Exception as e:
            logger.error(f"创建节点内容失败: {e}")
            return None

    async def update_node_content(
        self,
        node_id: str,
        content: str,
        prompt_used: str = "",
        model_name: str = "",
        provider_name: Optional[str] = None,
        generation_config: Optional[Dict[str, Any]] = None,
        created_by: str = "system"
    ) -> Optional[str]:
        """
        更新节点内容（创建新版本）

        Args:
            node_id: 节点ID
            content: 内容文本
            prompt_used: 使用的提示词
            model_name: 使用的模型名称
            provider_name: 模型提供商名称
            generation_config: 生成配置
            created_by: 创建者

        Returns:
            Optional[str]: 新版本ID
        """
        try:
            # 获取节点内容
            node_content = await self.get_node_content(node_id)
            if not node_content:
                logger.error(f"节点内容不存在: {node_id}")
                return None

            # 创建新版本
            version_id = await self.version_manager.create_version(
                node_content=node_content,
                content=content,
                prompt_used=prompt_used,
                model_name=model_name,
                provider_name=provider_name,
                generation_config=generation_config or {},
                created_by=created_by
            )

            logger.info(f"更新节点内容成功: {node_id}, 新版本: {version_id}")
            return version_id

        except Exception as e:
            logger.error(f"更新节点内容失败: {e}")
            return None

    async def get_tree_contents(self, tree_id: str) -> List[NodeContent]:
        """
        获取逻辑树的所有节点内容

        Args:
            tree_id: 逻辑树ID

        Returns:
            List[NodeContent]: 节点内容列表
        """
        return await self.storage.get_node_contents_by_tree(tree_id)

    async def reorder_nodes(
        self,
        tree_id: str,
        node_orders: Dict[str, int]
    ) -> bool:
        """
        重新排序节点

        Args:
            tree_id: 逻辑树ID
            node_orders: 节点ID到排序的映射

        Returns:
            bool: 是否排序成功
        """
        try:
            tree = await self.get_logic_tree(tree_id)
            if not tree:
                logger.error(f"逻辑树不存在: {tree_id}")
                return False

            # 更新节点排序
            for node_id, order in node_orders.items():
                if node_id in tree.nodes:
                    tree.nodes[node_id].update_order(order)

            # 保存逻辑树
            success = await self.update_logic_tree(tree)
            if success:
                logger.info(f"重新排序节点成功: {tree_id}")

            return success

        except Exception as e:
            logger.error(f"重新排序节点失败: {e}")
            return False

    async def clone_logic_tree(
        self,
        source_tree_id: str,
        new_name: str,
        new_project_id: Optional[str] = None
    ) -> Optional[LogicTree]:
        """
        克隆逻辑树

        Args:
            source_tree_id: 源逻辑树ID
            new_name: 新逻辑树名称
            new_project_id: 新项目ID，如果为None则使用源项目ID

        Returns:
            Optional[LogicTree]: 克隆的逻辑树
        """
        try:
            # 获取源逻辑树
            source_tree = await self.get_logic_tree(source_tree_id)
            if not source_tree:
                logger.error(f"源逻辑树不存在: {source_tree_id}")
                return None

            # 创建新逻辑树
            new_tree = LogicTree(
                name=new_name,
                project_id=new_project_id or source_tree.project_id,
                description=f"克隆自: {source_tree.name}"
            )

            # 复制节点
            node_id_mapping = {}  # 旧节点ID -> 新节点ID

            for old_node_id, old_node in source_tree.nodes.items():
                # 创建新节点
                new_node = LogicNode(
                    name=old_node.name,
                    node_type=old_node.node_type,
                    description=old_node.description,
                    order=old_node.order,
                    is_enabled=old_node.is_enabled,
                    is_required=old_node.is_required,
                    prompt_template=old_node.prompt_template,
                    generation_config=old_node.generation_config.copy()
                )

                # 记录ID映射
                node_id_mapping[old_node_id] = new_node.id
                new_tree.nodes[new_node.id] = new_node

            # 重建父子关系
            for old_node_id, old_node in source_tree.nodes.items():
                new_node_id = node_id_mapping[old_node_id]
                new_node = new_tree.nodes[new_node_id]

                # 设置父节点
                if old_node.parent_id and old_node.parent_id in node_id_mapping:
                    new_parent_id = node_id_mapping[old_node.parent_id]
                    new_node.parent_id = new_parent_id

                # 设置子节点
                new_node.children_ids = [
                    node_id_mapping[child_id]
                    for child_id in old_node.children_ids
                    if child_id in node_id_mapping
                ]

            # 设置根节点
            if source_tree.root_node_id and source_tree.root_node_id in node_id_mapping:
                new_tree.root_node_id = node_id_mapping[source_tree.root_node_id]

            # 保存新逻辑树
            success = await self.storage.save_logic_tree(new_tree)
            if success:
                logger.info(f"克隆逻辑树成功: {source_tree_id} -> {new_tree.id}")
                return new_tree
            else:
                logger.error(f"保存克隆逻辑树失败: {new_tree.id}")
                return None

        except Exception as e:
            logger.error(f"克隆逻辑树失败: {e}")
            return None

    async def get_tree_statistics(self, tree_id: str) -> Optional[Dict[str, Any]]:
        """
        获取逻辑树统计信息

        Args:
            tree_id: 逻辑树ID

        Returns:
            Optional[Dict[str, Any]]: 统计信息
        """
        try:
            tree = await self.get_logic_tree(tree_id)
            if not tree:
                return None

            contents = await self.get_tree_contents(tree_id)

            # 统计节点类型
            node_type_counts = {}
            for node in tree.nodes.values():
                node_type = node.node_type.value
                node_type_counts[node_type] = node_type_counts.get(node_type, 0) + 1

            # 统计内容状态
            content_status_counts = {}
            total_versions = 0
            for content in contents:
                current_version = content.get_current_version()
                if current_version:
                    status = current_version.status.value
                    content_status_counts[status] = content_status_counts.get(status, 0) + 1
                total_versions += len(content.versions)

            return {
                "tree_id": tree_id,
                "tree_name": tree.name,
                "project_id": tree.project_id,
                "total_nodes": len(tree.nodes),
                "total_contents": len(contents),
                "total_versions": total_versions,
                "node_type_counts": node_type_counts,
                "content_status_counts": content_status_counts,
                "created_at": tree.created_at,
                "updated_at": tree.updated_at
            }

        except Exception as e:
            logger.error(f"获取逻辑树统计信息失败: {e}")
            return None

    async def get_project_context(self, project_id: str) -> Optional[ProjectContext]:
        """
        从storage获取项目上下文

        Args:
            project_id: 项目ID

        Returns:
            Optional[ProjectContext]: 项目上下文
        """
        try:
            project_context = await self.storage.get_project_context(project_id)
            if project_context:
                logger.info(f"成功获取项目上下文: {project_id}")
            else:
                logger.warning(f"项目上下文不存在: {project_id}")
            return project_context
        except Exception as e:
            logger.error(f"获取项目上下文失败: {e}")
            return None

    async def save_project_context(self, project_context: ProjectContext) -> bool:
        """
        保存项目上下文到storage

        Args:
            project_context: 项目上下文

        Returns:
            bool: 是否保存成功
        """
        try:
            success = await self.storage.save_project_context(project_context)
            if success:
                logger.info(f"保存项目上下文成功: {project_context.project_id}")
            else:
                logger.error(f"保存项目上下文失败: {project_context.project_id}")
            return success
        except Exception as e:
            logger.error(f"保存项目上下文失败: {e}")
            return False

    async def delete_project_context(self, project_id: str) -> bool:
        """
        删除项目上下文

        Args:
            project_id: 项目ID

        Returns:
            bool: 是否删除成功
        """
        try:
            success = await self.storage.delete_project_context(project_id)
            if success:
                logger.info(f"删除项目上下文成功: {project_id}")
            else:
                logger.error(f"删除项目上下文失败: {project_id}")
            return success
        except Exception as e:
            logger.error(f"删除项目上下文失败: {e}")
            return False
