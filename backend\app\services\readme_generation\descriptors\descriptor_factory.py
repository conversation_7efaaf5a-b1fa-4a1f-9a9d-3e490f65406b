"""
描述器工厂

管理所有可用的README生成描述器（除了逻辑树描述器）
"""

import logging
from typing import Dict, List, Type, Optional

from ...execution_framework.base.step_type import StepType
from .base_generation_descriptor import BaseGenerationDescriptor
from .introduction_descriptor import IntroductionDescriptor
from .features_descriptor import FeaturesDescriptor
from .installation_descriptor import InstallationDescriptor
from .usage_descriptor import UsageDescriptor
from .architecture_descriptor import ArchitectureDescriptor
from .mermaid_diagram_descriptor import MermaidDiagramDescriptor
from .api_documentation_descriptor import ApiDocumentationDescriptor
from .deployment_descriptor import DeploymentDescriptor

logger = logging.getLogger(__name__)


class DescriptorFactory:
    """描述器工厂 - 管理所有可用的描述器"""
    
    def __init__(self):
        """初始化描述器工厂"""
        self._descriptors: Dict[str, Type[BaseGenerationDescriptor]] = {}
        self._register_default_descriptors()
    
    def _register_default_descriptors(self):
        """注册默认的描述器"""
        # 注册介绍描述器
        self.register_descriptor("introduction", IntroductionDescriptor)

        # 注册功能特性描述器
        self.register_descriptor("features", FeaturesDescriptor)

        # 注册安装说明描述器
        self.register_descriptor("installation", InstallationDescriptor)

        # 注册使用说明描述器
        self.register_descriptor("usage", UsageDescriptor)

        # 注册架构说明描述器
        self.register_descriptor("architecture", ArchitectureDescriptor)

        # 注册Mermaid架构图描述器
        self.register_descriptor("mermaid_diagram", MermaidDiagramDescriptor)

        # 注册API文档描述器
        self.register_descriptor("api_documentation", ApiDocumentationDescriptor)

        # 注册部署说明描述器
        self.register_descriptor("deployment", DeploymentDescriptor)
    
    def register_descriptor(self, name: str, descriptor_class: Type[BaseGenerationDescriptor]):
        """
        注册描述器
        
        Args:
            name: 描述器名称
            descriptor_class: 描述器类
        """
        self._descriptors[name] = descriptor_class
        logger.debug(f"注册描述器: {name}")
    
    def get_descriptor(self, name: str) -> Optional[BaseGenerationDescriptor]:
        """
        获取描述器实例
        
        Args:
            name: 描述器名称
            
        Returns:
            Optional[BaseGenerationDescriptor]: 描述器实例
        """
        descriptor_class = self._descriptors.get(name)
        if descriptor_class:
            return descriptor_class()
        return None
    
    def get_all_descriptors(self) -> Dict[str, BaseGenerationDescriptor]:
        """
        获取所有描述器实例
        
        Returns:
            Dict[str, BaseGenerationDescriptor]: 描述器名称到实例的映射
        """
        return {
            name: descriptor_class()
            for name, descriptor_class in self._descriptors.items()
        }
    
    def list_descriptor_names(self) -> List[str]:
        """
        列出所有描述器名称
        
        Returns:
            List[str]: 描述器名称列表
        """
        return list(self._descriptors.keys())
    
    def get_descriptor_info(self, name: str) -> Optional[Dict[str, str]]:
        """
        获取描述器信息
        
        Args:
            name: 描述器名称
            
        Returns:
            Optional[Dict[str, str]]: 描述器信息
        """
        descriptor = self.get_descriptor(name)
        if descriptor:
            return {
                "name": name,
                "step_type": descriptor.step_type.value,
                "description": descriptor.get_description(),
                "system_prompt": descriptor.get_system_prompt() or "无系统提示词"
            }
        return None
    
    def get_all_descriptor_info(self) -> List[Dict[str, str]]:
        """
        获取所有描述器信息
        
        Returns:
            List[Dict[str, str]]: 所有描述器信息列表
        """
        info_list = []
        for name in self.list_descriptor_names():
            info = self.get_descriptor_info(name)
            if info:
                info_list.append(info)
        return info_list
    
    def get_descriptors_by_step_type(self, step_type: StepType) -> List[str]:
        """
        根据步骤类型获取描述器

        Args:
            step_type: 步骤类型

        Returns:
            List[str]: 该步骤类型下的描述器名称列表
        """
        matching_descriptors = []
        for name, descriptor_class in self._descriptors.items():
            descriptor = descriptor_class()
            if descriptor.step_type == step_type:
                matching_descriptors.append(name)

        return matching_descriptors
    
    def format_descriptor_list_for_llm(self) -> str:
        """
        格式化描述器列表供LLM使用

        Returns:
            str: 格式化的描述器列表
        """
        descriptor_info = self.get_all_descriptor_info()

        if not descriptor_info:
            return "暂无可用的描述器"

        lines = ["## 可用的文档生成描述器\n"]

        for info in descriptor_info:
            lines.append(f"- **{info['name']}**: {info['description']}")

        return "\n".join(lines)



