"""
存储层基类

定义存储接口的抽象基类
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from ..models.logic_tree import LogicTree
from ..models.content import NodeContent
from ..models.project_context import ProjectContext


class BaseStorage(ABC):
    """存储层抽象基类"""
    
    @abstractmethod
    async def initialize(self) -> bool:
        """初始化存储"""
        pass
    
    @abstractmethod
    async def shutdown(self) -> None:
        """关闭存储"""
        pass
    
    # 逻辑树存储
    @abstractmethod
    async def save_logic_tree(self, tree: LogicTree) -> bool:
        """保存逻辑树"""
        pass
    
    @abstractmethod
    async def get_logic_tree(self, tree_id: str) -> Optional[LogicTree]:
        """获取逻辑树"""
        pass
    
    @abstractmethod
    async def get_logic_trees_by_project(self, project_id: str) -> List[LogicTree]:
        """根据项目ID获取逻辑树列表"""
        pass

    @abstractmethod
    async def get_all_logic_trees(self) -> List[LogicTree]:
        """获取全部逻辑树"""
        pass

    @abstractmethod
    async def delete_logic_tree(self, tree_id: str) -> bool:
        """删除逻辑树"""
        pass
    
    # 节点内容存储
    @abstractmethod
    async def save_node_content(self, content: NodeContent) -> bool:
        """保存节点内容"""
        pass
    
    @abstractmethod
    async def get_node_content(self, content_id: str) -> Optional[NodeContent]:
        """获取节点内容"""
        pass
    
    @abstractmethod
    async def get_node_content_by_node(self, node_id: str) -> Optional[NodeContent]:
        """根据节点ID获取内容"""
        pass
    
    @abstractmethod
    async def get_node_contents_by_tree(self, tree_id: str) -> List[NodeContent]:
        """根据逻辑树ID获取所有节点内容"""
        pass
    
    @abstractmethod
    async def delete_node_content(self, content_id: str) -> bool:
        """删除节点内容"""
        pass
    
    # 项目上下文存储
    @abstractmethod
    async def save_project_context(self, context: ProjectContext) -> bool:
        """保存项目上下文"""
        pass
    
    @abstractmethod
    async def get_project_context(self, project_id: str) -> Optional[ProjectContext]:
        """获取项目上下文"""
        pass
    
    @abstractmethod
    async def delete_project_context(self, project_id: str) -> bool:
        """删除项目上下文"""
        pass
    
    # 查询和统计
    @abstractmethod
    async def list_projects(self) -> List[str]:
        """列出所有项目ID"""
        pass
    
    @abstractmethod
    async def get_storage_statistics(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """健康检查"""
        pass
