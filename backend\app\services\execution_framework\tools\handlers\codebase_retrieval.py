"""
代码库检索工具处理器
"""

import logging
from typing import Dict, Any, List, Type, Optional
from pydantic import BaseModel, Field

from ...models.context import ExecutionContext
from ..base_handler import BaseToolHandler
from ....codebase.manager import CodeBaseManager
from ....codebase.models import CodeSearchQuery

logger = logging.getLogger(__name__)


class CodebaseRetrievalInput(BaseModel):
    """代码库检索工具输入参数"""
    information_request: str = Field(description="信息请求描述")


class CodebaseRetrievalHandler(BaseToolHandler):
    """代码库检索工具处理器"""

    def __init__(self, codebase_manager: Optional[CodeBaseManager] = None):
        super().__init__("codebase-retrieval")
        self.codebase_manager = codebase_manager

    def get_args_schema(self) -> Type[BaseModel]:
        """获取参数模型"""
        return CodebaseRetrievalInput

    async def execute(self, parameters: Dict[str, Any], context: ExecutionContext) -> Any:
        """执行代码库检索"""
        information_request = parameters.get('information_request', '')

        logger.info(f"执行代码库检索: {information_request}")

        # 如果没有代码库管理器，返回错误信息
        if not self.codebase_manager:
            logger.warning("代码库管理器未初始化，无法执行检索")
            return {
                "query": information_request,
                "error": "代码库管理器未初始化",
                "timestamp": context.current_time
            }

        try:
            # 创建搜索查询
            search_query = CodeSearchQuery(
                query=information_request,
                top_k=10,  # 默认返回10个结果
                include_content=True,
                include_context=False,
                similarity_threshold=0.4
            )

            # 执行搜索
            search_results = await self.codebase_manager.search_code(search_query)

            # 格式化结果
            formatted_results = []
            for result in search_results:
                formatted_results.append({
                    "id": result.block.id,
                    "name": result.block.name,
                    "type": result.block.type.value if result.block.type else None,
                    "content": result.block.content,
                    "language": result.block.language.value if result.block.language else None,
                    "file_path": result.block.file_path,
                    "start_line": result.block.position.start_line,
                    "end_line": result.block.position.end_line,
                    "start_column": result.block.position.start_column,
                    "end_column": result.block.position.end_column,
                    "signature": result.block.signature,
                    "docstring": result.block.docstring,
                    "lines_of_code": result.block.lines_of_code,
                    "similarity_score": result.similarity_score,
                    "highlights": result.highlights,
                    "metadata": result.metadata
                })

            return {
                "query": information_request,
                "results": formatted_results,
                "total_results": len(formatted_results),
                "timestamp": context.current_time
            }

        except Exception as e:
            logger.error(f"代码库检索失败: {e}")
            return {
                "query": information_request,
                "error": f"检索失败: {str(e)}",
                "timestamp": context.current_time
            }

    def validate_parameters(self, parameters: Dict[str, Any]) -> List[str]:
        """验证参数"""
        errors = []
        if not parameters.get('information_request'):
            errors.append("缺少必需参数: information_request")
        return errors
