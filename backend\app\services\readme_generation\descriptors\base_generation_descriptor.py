"""
基础生成描述器

为README生成器提供简洁的描述器基类，重点关注提示词和工具指导
"""

import logging
from abc import abstractmethod
from typing import List, Dict, Any, Optional

from ...execution_framework.base.descriptor import BaseDescriptor
from ...execution_framework.base.step_type import StepType
from ...execution_framework.models.context import ExecutionContext
from ...execution_framework.models.descriptor_config import DescriptorConfig
from ...execution_framework.models.tool_instruction import ToolInstruction

logger = logging.getLogger(__name__)


class BaseGenerationDescriptor(BaseDescriptor):
    """README生成描述器基类 - 专注于提示词指导"""

    def __init__(self, step_type: StepType, name: Optional[str] = None):
        """初始化生成描述器"""
        super().__init__(step_type, name)

    def get_descriptor_config(self, context: ExecutionContext) -> Optional[DescriptorConfig]:
        """获取描述器配置 - 专注于提示词相关配置"""
        return DescriptorConfig(
            template=self.get_prompt_template(),
            input_variables=self.get_input_variables(),
            system_prompt=self.get_system_prompt(),
            constraints=self.get_constraints(),
            output_format="markdown"
        )

    @abstractmethod
    def get_prompt_template(self) -> str:
        """获取提示词模板 - 指导LLM如何使用工具和分析内容"""
        pass

    @abstractmethod
    def get_input_variables(self) -> List[str]:
        """获取输入变量列表 - 模板中需要的变量"""
        pass

    def get_system_prompt(self) -> Optional[str]:
        """获取系统提示词 - 可选的系统级指导"""
        return None

    def get_constraints(self) -> List[str]:
        """获取约束条件 - 生成内容的限制条件"""
        return [
            "内容必须基于实际项目信息",
            "避免重复内容",
            "保持专业性和准确性"
        ]
