"""
节点内容处理器
"""

import json
from typing import Dict, Any, Optional
from tornado.web import HTTPError
import structlog
from dependency_injector.wiring import inject, Provide

from app.api.base import BaseHandler
from app.core.di.containers import Container
from app.services.readme_generation.generation_manager import GenerationManager

logger = structlog.get_logger(__name__)


class NodeContentHandler(BaseHandler):
    """节点内容处理器"""

    @inject
    def initialize(
        self,
        readme_generation_manager: GenerationManager = Provide[Container.readme_generation_manager],
    ):
        super().initialize()
        self.readme_generation_manager = readme_generation_manager

    async def post(self):
        """生成节点内容"""
        try:
            if not self.json_body:
                raise HTTPError(400, "请求体不能为空")

            tree_id = self.json_body.get("tree_id")
            node_id = self.json_body.get("node_id")
            model_name = self.json_body.get("model_name", "default")
            provider_name = self.json_body.get("provider_name")
            custom_prompt = self.json_body.get("custom_prompt")
            created_by = self.json_body.get("created_by", "api_user")

            if not tree_id or not node_id:
                raise HTTPError(400, "tree_id和node_id参数是必需的")

            # 生成节点内容
            logger.info("开始生成节点内容", tree_id=tree_id, node_id=node_id)
            result = await self.readme_generation_manager.generate_node(
                tree_id=tree_id,
                node_id=node_id,
                model_name=model_name,
                provider_name=provider_name,
                custom_prompt=custom_prompt,
                created_by=created_by
            )

            if not result.get("success"):
                error_msg = result.get("error", "节点内容生成失败")
                raise HTTPError(500, error_msg)

            node_content = result.get("node_content")

            # 返回生成结果
            response_data = {
                "id": node_content.id,
                "node_content": node_content.model_dump(),
                "message": result.get("message", "节点内容生成成功")
            }

            self.success_response(response_data, "节点内容生成成功")

        except HTTPError:
            raise
        except Exception as e:
            logger.error("节点内容生成失败", error=str(e), exc_info=e)
            raise HTTPError(500, f"节点内容生成失败: {str(e)}")
    
    async def put(self):
        """重新生成节点内容"""
        try:
            if not self.json_body:
                raise HTTPError(400, "请求体不能为空")

            tree_id = self.json_body.get("tree_id")
            node_id = self.json_body.get("node_id")
            model_name = self.json_body.get("model_name", "default")
            provider_name = self.json_body.get("provider_name")
            custom_prompt = self.json_body.get("custom_prompt")
            created_by = self.json_body.get("created_by", "api_user")

            if not tree_id or not node_id:
                raise HTTPError(400, "tree_id和node_id参数是必需的")

            # 重新生成节点内容
            logger.info("开始重新生成节点内容", tree_id=tree_id, node_id=node_id)
            result = await self.readme_generation_manager.regenerate_node(
                tree_id=tree_id,
                node_id=node_id,
                model_name=model_name,
                provider_name=provider_name,
                custom_prompt=custom_prompt,
                created_by=created_by
            )

            if not result.get("success"):
                error_msg = result.get("error", "节点内容重新生成失败")
                raise HTTPError(500, error_msg)
            node_content = result.get("node_content")
            # 返回重新生成结果
            response_data = {
                "id": node_content.id,
                "node_content": node_content.model_dump(),
                "regeneration_reason": result.get("regeneration_reason", "用户请求重新生成"),
                "message": result.get("message", "节点内容重新生成成功")
            }

            self.success_response(response_data, "节点内容重新生成成功")

        except HTTPError:
            raise
        except Exception as e:
            logger.error("节点内容重新生成失败", error=str(e), exc_info=e)
            raise HTTPError(500, f"节点内容重新生成失败: {str(e)}")

    async def get(self, node_id: str):
        """获取节点内容"""
        try:
            if not node_id:
                raise HTTPError(400, "node_id参数是必需的")

            # 获取节点内容
            logger.info("获取节点内容", node_id=node_id)
            node_content = await self.readme_generation_manager.get_node_content(node_id)

            if not node_content:
                raise HTTPError(404, "节点内容不存在")

            # 返回节点内容
            response_data = {
                "id": node_content.id,
                "node_content": node_content.model_dump(),
                "message": "节点内容获取成功"
            }

            self.success_response(response_data, "节点内容获取成功")

        except HTTPError:
            raise
        except Exception as e:
            logger.error("获取节点内容失败", error=str(e), exc_info=e)
            raise HTTPError(500, f"获取节点内容失败: {str(e)}")
