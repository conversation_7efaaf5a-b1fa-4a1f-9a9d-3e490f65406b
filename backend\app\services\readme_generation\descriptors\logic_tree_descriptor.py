"""
逻辑树生成描述器

通过提示词指导LLM分析项目并规划文档结构
"""

import logging
from typing import List

from ...execution_framework.base.step_type import StepType
from ...execution_framework.models.context import ExecutionContext
from ...execution_framework.models.tool_instruction import ToolInstruction
from ...execution_framework.models.descriptor_config import DescriptorConfig
from .base_generation_descriptor import BaseGenerationDescriptor
from .descriptor_factory import DescriptorFactory

logger = logging.getLogger(__name__)


class LogicTreeDescriptor(BaseGenerationDescriptor):
    """逻辑树生成描述器 - 指导LLM智能分析项目特征"""

    def __init__(self):
        """初始化逻辑树描述器"""
        super().__init__(StepType.PLANNING, "logic_tree")
        # 初始化描述器工厂
        self._descriptor_factory = DescriptorFactory()

    def get_description(self) -> str:
        """获取描述器描述"""
        return "指导LLM分析项目特征并规划README文档结构"

    def get_tool_instructions(self, context: ExecutionContext) -> List[ToolInstruction]:
        """获取工具调用指令列表 - 每个工具只定义一次"""
        return [
            ToolInstruction(
                name="view-directory",
                parameters={"path": "."},
                description="查看项目根目录结构，了解项目组织方式",
                result_key="directory_structure"
            ),
            ToolInstruction(
                name="codebase-retrieval",
                parameters={
                    "information_request": "项目整体架构、技术栈、主要功能模块、复杂度特征"
                },
                description="深度分析项目代码库特征",
                result_key="codebase_analysis"
            ),
            ToolInstruction(
                name="view-file",
                parameters={"path": "README.md"},
                description="查看现有README文件（如果存在）",
                result_key="existing_readme"
            )
        ]

    def get_prompt_template(self) -> str:
        """获取提示词模板 - 指导LLM如何分析项目并规划文档结构"""
        # 获取可用描述器信息
        available_descriptors = self._descriptor_factory.format_descriptor_list_for_llm()
        
        return f"""
## 项目信息
项目名称: {{project_name}}
项目路径: {{project_path}}
项目类型: {{project_type}}
编程语言: {{programming_languages}}
技术框架: {{frameworks}}

## 项目结构
{{directory_structure}}

## 代码库分析
{{codebase_analysis}}

## 现有README
{{existing_readme}}

## 自定义要求
{{custom_prompt}}

{available_descriptors}

## 分析任务

请基于以上项目信息，结合可用的描述器，完成文档结构规划。
"""

    def get_input_variables(self) -> List[str]:
        """获取输入变量列表"""
        return [
            "project_name",
            "project_path",
            "project_type",
            "programming_languages",
            "frameworks",
            "directory_structure",
            "codebase_analysis",
            "existing_readme",
            "custom_prompt"
        ]

    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return "你是一个专业的技术文档架构师，擅长分析项目特征并规划文档结构。"

    def get_constraints(self) -> List[str]:
        """约束条件迁移自模板的规划要求"""
        return [
            "考虑分析结果与自定义要求，规划应优先满足自定义要求",
            "给出项目分析结果：类型、复杂度、技术栈、主要功能模块",
            "从可用描述器中选择合适的描述器并说明理由，包含如何满足自定义要求",
            "为每个描述器设置1-10的优先级，并给出执行顺序与依赖",
            "提供避免重复的策略，并说明如何整合自定义要求",
        ]

    def get_descriptor_config(self, context: ExecutionContext) -> DescriptorConfig:
        """覆盖父类，提供JSON输出格式定义"""
        json_schema = (
            "```json\n"
            "{\n"
            "  \"project_analysis\": {\n"
            "    \"type\": \"项目类型\",\n"
            "    \"complexity\": \"复杂度评估\",\n"
            "    \"tech_stack\": [\"技术栈列表\"],\n"
            "    \"main_features\": [\"主要功能列表\"],\n"
            "    \"custom_requirements_analysis\": \"对自定义要求的分析和理解\"\n"
            "  },\n"
            "  \"selected_descriptors\": [\n"
            "    {\n"
            "      \"name\": \"描述器名称\",\n"
            "      \"reason\": \"选择理由（包括如何满足自定义要求）\",\n"
            "      \"priority\": 1,\n"
            "      \"custom_alignment\": \"与自定义要求的匹配度说明\"\n"
            "    }\n"
            "  ],\n"
            "  \"execution_plan\": {\n"
            "    \"order\": [\"描述器执行顺序\"],\n"
            "    \"dependencies\": {\"描述器\": [\"依赖的描述器\"]},\n"
            "    \"anti_duplication_strategy\": \"避免重复的策略\",\n"
            "    \"custom_requirements_integration\": \"如何在执行过程中整合自定义要求\"\n"
            "  }\n"
            "}\n"
            "```"
        )

        return DescriptorConfig(
            template=self.get_prompt_template(),
            input_variables=self.get_input_variables(),
            system_prompt=self.get_system_prompt(),
            constraints=self.get_constraints(),
            output_format=json_schema,
        )

    def get_priority(self, context: ExecutionContext) -> int:
        """获取优先级 - 逻辑树规划应该最先执行"""
        return 200

    def get_dependencies(self) -> List[str]:
        """获取依赖 - 逻辑树生成是规划阶段，无依赖"""
        return []
