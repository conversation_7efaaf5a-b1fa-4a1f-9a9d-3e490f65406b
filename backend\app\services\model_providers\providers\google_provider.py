"""
Google模型提供商
"""

from typing import List
import structlog

from ..base import BaseModelProvider
from ..models import (
    ModelConfig, ModelProviderConfig, ModelCapability, 
    ModelStatus, ModelProviderType
)

logger = structlog.get_logger(__name__)


class GoogleProvider(BaseModelProvider):
    """Google模型提供商"""
    
    async def _initialize(self) -> None:
        """初始化Google提供商"""
        # 验证API密钥
        if not self.config.api_key:
            raise ValueError("Google API密钥未配置")
        
        # 设置默认API基础URL
        if not self.config.api_base:
            self.config.api_base = "https://generativelanguage.googleapis.com/v1beta"
        
        logger.info(f"Google提供商初始化完成，API基础URL: {self.config.api_base}")
    
    async def _get_models(self) -> List[ModelConfig]:
        """获取Google模型列表"""
        # 只从配置中获取模型，不使用硬编码
        if not self.config.models:
            logger.warning("Google提供商配置中没有定义模型")
            return []

        return self.config.models.copy()
    
    async def _validate_model(self, model: ModelConfig) -> bool:
        """验证Google模型"""
        return model.status == ModelStatus.AVAILABLE
