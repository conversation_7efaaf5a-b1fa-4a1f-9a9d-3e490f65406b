# @GeneratedBy:AI
"""
arXiv论文处理相关Schema
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class ArxivPaperInfo(BaseModel):
    """arXiv论文信息"""
    paper_id: str = Field(..., description="论文ID")
    title: str = Field(..., description="论文标题")
    authors: List[str] = Field(default_factory=list, description="作者列表")
    abstract: str = Field("", description="摘要")
    content: str = Field("", description="内容")
    categories: List[str] = Field(default_factory=list, description="分类标签")
    created_date: Optional[datetime] = Field(None, description="创建时间")
    updated_date: Optional[datetime] = Field(None, description="更新时间")


class ArxivChunkInfo(BaseModel):
    """arXiv论文分段信息"""
    chunk_id: str = Field(..., description="分段ID")
    paper_id: str = Field(..., description="所属论文ID")
    content: str = Field(..., description="分段内容")
    chunk_index: int = Field(..., description="分段索引")
    chunk_type: str = Field("content", description="分段类型(title/abstract/content)")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class ArxivProcessRequest(BaseModel):
    """arXiv处理请求"""
    max_files: int = Field(100, description="最大处理文件数量", ge=1, le=1000)
    max_papers_per_file: int = Field(100, description="每个文件最大处理论文数", ge=1, le=500)
    embedding_model: str = Field("tei_bge_base_zh", description="嵌入模型名称")
    vector_store: str = Field("milvus_quality", description="向量存储名称")
    chunk_size: int = Field(1000, description="文本分段大小", ge=100, le=5000)
    chunk_overlap: int = Field(200, description="分段重叠大小", ge=0, le=1000)


class ArxivProcessResponse(BaseModel):
    """arXiv处理响应"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="处理状态")
    message: str = Field(..., description="响应消息")
    processed_files: int = Field(0, description="已处理文件数")
    total_papers: int = Field(0, description="提取的论文总数")
    total_chunks: int = Field(0, description="创建的分段总数")
    total_vectors: int = Field(0, description="创建的向量总数")


class ArxivSearchRequest(BaseModel):
    """arXiv搜索请求"""
    query: str = Field(..., description="搜索查询", min_length=1)
    top_k: int = Field(10, description="返回结果数量", ge=1, le=100)
    similarity_threshold: float = Field(0.7, description="相似度阈值", ge=0.0, le=1.0)
    search_type: str = Field("semantic", description="搜索类型(semantic/keyword/hybrid)")
    filters: Optional[Dict[str, Any]] = Field(None, description="过滤条件")


class ArxivSearchResponse(BaseModel):
    """arXiv搜索响应"""
    query: str = Field(..., description="搜索查询")
    results: List[ArxivChunkInfo] = Field(default_factory=list, description="搜索结果")
    total_results: int = Field(0, description="总结果数")
    search_time: float = Field(0.0, description="搜索耗时(秒)")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="搜索元数据")


class ArxivStatusResponse(BaseModel):
    """arXiv处理状态响应"""
    total_files: int = Field(0, description="总文件数")
    processed_files: int = Field(0, description="已处理文件数")
    pending_files: int = Field(0, description="待处理文件数")
    error_files: int = Field(0, description="错误文件数")
    processing_rate: float = Field(0.0, description="处理进度百分比")
    total_papers: int = Field(0, description="总论文数")
    total_vectors: int = Field(0, description="总向量数")
