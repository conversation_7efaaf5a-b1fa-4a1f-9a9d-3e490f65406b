"""
功能特性生成描述器

通过提示词指导LLM生成项目功能特性说明
"""

import logging
from typing import List

from ...execution_framework.base.step_type import StepType
from ...execution_framework.models.context import ExecutionContext
from ...execution_framework.models.tool_instruction import ToolInstruction
from .base_generation_descriptor import BaseGenerationDescriptor

logger = logging.getLogger(__name__)


class FeaturesDescriptor(BaseGenerationDescriptor):
    """项目功能特性生成描述器 - 指导LLM生成功能特性说明"""

    def __init__(self):
        """初始化功能特性生成描述器"""
        super().__init__(StepType.FEATURES, "features")

    def get_description(self) -> str:
        """获取描述器描述"""
        return "指导LLM生成项目功能特性和核心能力说明"

    def get_tool_instructions(self, context: ExecutionContext) -> List[ToolInstruction]:
        """获取工具调用指令列表 - 每个工具只定义一次"""
        return [
            ToolInstruction(
                name="codebase-retrieval",
                parameters={
                    "information_request": "项目核心功能模块、主要特性、技术亮点、创新点、性能优势"
                },
                description="获取项目功能特性相关信息",
                result_key="codebase_analysis"
            ),
            ToolInstruction(
                name="view-directory",
                parameters={"path": "."},
                description="查看项目结构了解功能模块",
                result_key="directory_structure"
            )
        ]

    def get_prompt_template(self) -> str:
        """获取提示词模板 - 指导LLM如何生成功能特性"""
        return """
## 项目信息
项目名称: {project_name}
项目路径: {project_path}
项目类型: {project_type}
编程语言: {programming_languages}
技术框架: {frameworks}

## 代码库分析
{codebase_analysis}

## 项目结构
{directory_structure}

## 自定义要求
{custom_prompt}

## 生成任务

请基于以上项目信息，使用可用工具（如 codebase-retrieval、view-directory）深入分析后，生成项目功能特性内容。

重点关注：
1. 核心功能模块和能力
2. 技术特色和创新点
3. 性能优势和亮点
4. 用户价值和应用场景
"""

    def get_input_variables(self) -> List[str]:
        """获取输入变量列表"""
        return [
            "project_name",
            "project_path",
            "project_type",
            "programming_languages",
            "frameworks",
            "codebase_analysis",
            "directory_structure",
            "custom_prompt"
        ]

    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return "你是一个专业的产品经理和技术专家，擅长提炼和展示项目的核心功能特性。"

    def get_constraints(self) -> List[str]:
        """将写作要求迁移为约束条件"""
        return [
            "使用Markdown格式",
            "突出项目的核心价值和技术优势",
            "内容必须基于实际代码分析和项目结构",
            "输出包含：核心功能列表（4-8个主要特性）、技术亮点、性能优势、应用场景",
            "每个特性用简洁的标题和2-3句话描述",
            "总长度控制在600-1000字",
        ]

    def get_priority(self, context: ExecutionContext) -> int:
        """获取优先级 - 功能特性优先级较高"""
        return 90

    def get_dependencies(self) -> List[str]:
        """获取依赖 - 可能依赖介绍部分"""
        return ["introduction"]
