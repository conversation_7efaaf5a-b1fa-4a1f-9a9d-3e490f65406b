<template>
  <div class="comment" v-for="(item, index) in props.data" :key="index">
    <div v-if="allMsgStatusFun(item)">
      <div
        v-if="allMsgStatusFun1(item)"
        class="comment-cont"
        :id="item.id"
        :style="{
          padding: commentContPadding(item),
        }">
        <div class="comment-top">
          <img class="avatar" src="@/assets/images/profile.png" />
          <div class="name">
            <span>{{ item.user_name }}</span>
            <template v-if="sendName">
              <template v-if="item.parent_id === item.root_id"></template>
              <template v-else>
                <span style="margin: 0 5px">回复</span>
                <span>
                  {{ props.sendName }}
                </span>
              </template>
            </template>
          </div>
        </div>
        <div class="comment-bottom">
          <div
            :class="{
              'mobile-comment-bottom': device === 'mobile',
            }"
            style="display: flex; align-items: flex-end">
            <div
              v-html="highlightWord(item.content)"
              :style="{
                maxWidth: device === 'mobile' ? '100%' : '90%',
              }"></div>
          </div>
          <div class="comment-bottom-operate">
            <div>
              <div class="time">
                {{ getLastTimeStr(item.updated_at, true) }}
              </div>
              <div
                v-if="userStore.replyName !== item.id"
                class="operate"
                @click="handleReply(item.id)">
                <el-icon class="icon" :size="16">
                  <ChatDotSquare />
                </el-icon>
                <span>评论</span>
              </div>
              <div
                v-else
                class="operate"
                style="color: #1e80ff"
                @click.stop="handleCancel">
                <el-icon class="icon" :size="16" color="#1e80ff">
                  <Comment />
                </el-icon>
                <span>取消评论</span>
              </div>
            </div>
            <template v-if="item.user_id === userStore.userInfo.id">
              <el-popover
                popper-class="comment-box-item"
                placement="left-start"
                :show-arrow="false">
                <template #reference>
                  <el-icon style="cursor: pointer">
                    <MoreFilled color="#656464" :size="16" />
                  </el-icon>
                </template>
                <div class="comment-delete" @click="handleDelete(item.id)">
                  删除
                </div>
              </el-popover>
            </template>
          </div>
          <template
            v-if="
              item.index === userStore.allMsgStatus[item.root_id] - 1 &&
              item.parent_id
            ">
            <el-link
              underline="never"
              style="margin-top: 6px"
              @click="handleShowAll(item.root_id, item.parent_id)"
              v-if="
                commentIndex[item.root_id] >
                userStore.allMsgStatus[item.root_id]
              ">
              显示全部{{ commentIndex[item.root_id] }}条评论
              <el-icon style="margin-left: 2px"><ArrowDown /></el-icon>
            </el-link>
          </template>
          <div
            class="comment-drawer-content"
            v-if="userStore.replyName === item.id">
            <el-mention
              ref="textareaRef"
              class="textarea"
              v-model="textarea"
              :autosize="{ minRows: 3, maxRows: 8 }"
              maxlength="1000"
              type="textarea"
              show-word-limit
              :placeholder="`回复 ${item.user_name}`"
              :options="options"
              @focus="focus"
              @blur="blur"
              @input="handleInput" />
            <img
              class="emoji"
              src="@/assets/images/guguda.png"
              style="left: 40px"
              @click.stop="handleAiTe" />
            <el-popover
              placement="bottom"
              :width="306"
              popper-class="emoji-popover">
              <div>
                <EmojiPicker
                  :hide-search="true"
                  :native="true"
                  @select="onSelectEmoji"
                  :disabled-groups="[
                    'travel_places',
                    'flags',
                    'symbols',
                    'objects',
                    'activities',
                  ]"
                  :hide-group-names="true" />
              </div>
              <template #reference>
                <img
                  class="emoji"
                  src="@/assets/images/emoji.png"
                  @click.stop="handleEmoji" />
              </template>
            </el-popover>

            <div class="btns">
              <el-button
                class="button"
                type="primary"
                :disabled="disabled"
                @click.stop="handleSend(item.id)">
                发送
              </el-button>
            </div>
          </div>
        </div>
      </div>
      <div
        style="margin-left: 35px; margin-top: 5px"
        :style="{
          marginLeft: !item.parent_id ? '35px' : 0,
        }">
        <comment
          :data="item.replies"
          :sendName="item.user_name"
          :articleId="props.articleId"
          :rootIds="props.rootIds"
          :commentIndex="props.commentIndex"></comment>
      </div>
    </div>
  </div>
</template>

<script setup>
import EmojiPicker from "vue3-emoji-picker";
import "vue3-emoji-picker/css";
import { commentCreat, commentDelete } from "@/api/article";
import { getLastTimeStr } from "@/utils/validate";
import useUserStore from "@/store/modules/user";
import useAppStore from "@/store/modules/app";
import { getCurrentInstance, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { ArrowDown, ChatDotSquare } from "@element-plus/icons-vue";
import { getToken } from "@/utils/auth";
const device = computed(() => useAppStore().device);
const textareaRef = useTemplateRef("textareaRef");
const userStore = useUserStore();

defineOptions({
  name: "comment",
});

const options = ref([
  {
    label: "咕咕答",
    value: "咕咕答",
  },
]);

const disabled = ref(true);
const textarea = ref("");
const props = defineProps({
  data: {
    type: Object,
    default: [],
  },
  sendName: {
    type: String,
    default: "",
  },
  articleId: {
    type: String,
    default: "",
  },
  rootIds: {
    type: Array,
    default: [],
  },
  commentIndex: {
    type: Object,
    default: {},
  },
});

/**
 * 评论项的padding
 * @param item 评论项
 * @returns 评论项的padding
 */
const commentContPadding = (item) => {
  let padding = "";
  if (!item.parent_id) {
    padding = 0;
  } else {
    padding = "12px 0 0 0";
  }
  return padding;
};

const router = useRouter();
const handleReply = (id) => {
  if (getToken()) {
    userStore.replyName = id;
    userStore.showCommentInput = false;
    textarea.value = "";
    nextTick(() => {
      textareaRef.value[0].input.focus();
    });
  } else {
    ElMessage({
      message: "请登录后评论",
      type: "warning",
    });
    if (device.value === "mobile") {
      router.push("/mobileLogin");
    } else {
      userStore.loginDialogVisible = true;
    }
  }
};
const highlightWord = (str, word = "@咕咕答") => {
  return str.replaceAll(word, '<span class="hignlight">@咕咕答</span>');
};
const { proxy } = getCurrentInstance();
const handleDelete = (id) => {
  commentDelete({
    comment_id: id,
  }).then((res) => {
    if (res.code === 200) {
      proxy.$modal.msgSuccess(`删除成功`);
      userStore.refreshComment = true;
      const timer = setTimeout(() => {
        userStore.refreshComment = false;
        clearTimeout(timer);
      }, 1000);
    }
  });
};

const handleSend = (parent_id) => {
  commentCreat({
    content: textarea.value,
    parent_id,
    project_id: props.articleId,
  }).then((res) => {
    if (res.code === 200) {
      userStore.replyName = "";
      userStore.refreshComment = true;
      userStore.comment_count += 1;
      const timer = setTimeout(() => {
        userStore.refreshComment = false;
        clearTimeout(timer);
      }, 1000);
    }
  });
};

const handleCancel = () => {
  userStore.replyName = "";
  textarea.value = "";
};

const onSelectEmoji = (emoji) => {
  textarea.value = textarea.value + emoji.i;
  textareaRef.value[0].input.focus();
};

const handleEmoji = () => {
  if (device.value === "mobile") {
    textareaRef.value[0].input.focus();
  }
};

const handleAiTe = () => {
  textarea.value = textarea.value + "@咕咕答";
  textareaRef.value[0].input.focus();
};

const handleShowAll = (root_id, parent_id) => {
  if (parent_id) {
    userStore.allMsgStatus[root_id] = 99;
  } else {
    userStore.allMsgStatus["root"] = 99;
  }
};

const allMsgStatusFun = (item) => {
  if (item.parent_id === null) {
    return item.index < userStore.allMsgStatus.root;
  } else {
    return true;
  }
};

const allMsgStatusFun1 = (item) => {
  if (item.parent_id !== null) {
    return item.index < userStore.allMsgStatus[item.root_id];
  } else {
    return true;
  }
};

watch(
  () => [props.sendName, props.rootIds],
  (newVal) => {
    if (newVal[0] === "") {
      newVal[1] &&
        newVal[1].forEach((item) => {
          userStore.allMsgStatus[item] = 2;
          userStore.allMsgStatus["root"] = 2;
        });
    }
  },
  {
    immediate: true,
    deep: true,
  }
);

const color = ref("#1e80ff");

const focus = () => {
  color.value = "#1e80ff";
  userStore.showCommentInput = false;
};

const blur = () => {
  color.value = "#e4e6eb";
};

const handleInput = () => {
  console.log(textarea.value, "=======");

  if (textarea.value) {
    disabled.value = false;
  } else {
    disabled.value = true;
  }
};
</script>
<style scoped lang="scss">
::v-deep(.v3-emoji-picker .v3-footer) {
  display: none;
}
::v-deep(.v3-emoji-picker .v3-header) {
  display: none;
}
::v-deep(.v3-emoji-picker) {
  box-shadow: none;
}
.comment {
  .comment-top {
    display: flex;
    align-items: center;

    .avatar {
      width: 30px;
      height: 30px;
    }

    .name {
      margin-left: 5px;
      font-size: 14px;
      color: #000000;
      font-weight: 800;
    }
  }

  .comment-bottom {
    margin-left: 35px;
    font-size: 14px;
    color: #000000;

    ::v-deep(.hignlight) {
      color: #0a78b2;
      margin: 0 2px;
    }

    .comment-bottom-operate {
      margin-top: 8px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      & > div {
        display: flex;
        align-items: center;
      }

      .time {
        font-size: 14px;
        color: #656464;
      }

      .operate {
        display: flex;
        align-items: center;
        cursor: pointer;
        font-size: 14px;
        color: #656464;

        &:hover {
          color: #1e80ff;
        }
      }

      .icon {
        margin: 0 2px 0 6px;
      }
    }

    .comment-drawer-content {
      margin-top: 4px;
      position: relative;
      padding: 6px;
      outline: 1px solid v-bind(color);
      border-radius: 4px;

      .emoji {
        width: 20px;
        position: absolute;
        bottom: 10px;
        left: 6px;
        cursor: pointer;
      }

      ::v-deep(.el-textarea__inner) {
        resize: none;
        box-shadow: none;
      }
      ::v-deep(.el-input__count) {
        bottom: -25px;
        right: 80px;
      }
      .textarea {
        font-size: 14px;

        textarea {
          resize: none;
          box-shadow: none;
        }

        .el-input__count {
          bottom: -25px;
          right: 80px;
        }
      }

      .btns {
        display: flex;
        flex-direction: row-reverse;
      }
    }
  }

  .mobile-comment-bottom {
    display: flex;
    justify-content: space-between;

    & > div:first-child {
      flex: 1;
    }
  }
}
</style>
<style lang="scss">
.comment-box-item {
  padding: 6px !important;
  .comment-delete {
    height: 24px;
    padding-left: 6px;
    font-size: 14px;
    color: #656464;
    line-height: 24px;
    cursor: pointer;
    &:hover {
      background-color: #f5f7fa;
    }
  }
}
</style>
