"""
AI驱动的执行引擎

基于LangChain实现的AI驱动执行引擎，整合描述器、工具调用、流程编排等功能
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Union, Callable
from datetime import datetime
from uuid import uuid4

from .base.descriptor import BaseDescriptor
from .base.step_type import StepType
from .models.context import ExecutionContext, ExecutionContextBuilder
from .models.tool_instruction import ToolInstruction
from .tools.tool_manager import ToolManager
from .executor.descriptor_executor import DescriptorExecutor
from .executor.workflow_orchestrator import WorkflowOrchestrator, ExecutionStatus
from .executor.monitoring import ExecutionMonitor
from ..ai_agent_core.model_config_manager import ModelConfigManager
from ..model_providers.manager import ModelProviderManager
from ..codebase.manager import CodeBaseManager

logger = logging.getLogger(__name__)


class ExecutionEngine:
    """AI驱动的执行引擎"""
    
    def __init__(
        self,
        model_provider_manager: Optional[ModelProviderManager] = None,
        tool_manager: Optional[ToolManager] = None,
        codebase_manager: Optional[CodeBaseManager] = None,
        descriptor_executor: Optional[DescriptorExecutor] = None,
        workflow_orchestrator: Optional[WorkflowOrchestrator] = None,
        execution_monitor: Optional[ExecutionMonitor] = None
    ):
        """
        初始化执行引擎

        Args:
            model_provider_manager: 模型提供商管理器
            tool_manager: 工具管理器
            codebase_manager: 代码库管理器
            descriptor_executor: 描述器执行器
            workflow_orchestrator: 工作流编排器
            execution_monitor: 执行监控器
        """
        self.model_provider_manager = model_provider_manager or ModelProviderManager()
        self.codebase_manager = codebase_manager
        self.tool_manager = tool_manager or ToolManager(codebase_manager)
        self.descriptor_executor = descriptor_executor or DescriptorExecutor(
            self.model_provider_manager,
            self.tool_manager,
            codebase_manager
        )
        self.workflow_orchestrator = workflow_orchestrator or WorkflowOrchestrator(
            self.tool_manager,
            self.descriptor_executor
        )
        self.execution_monitor = execution_monitor or ExecutionMonitor()
        
        # 注册的描述器
        self.descriptors: Dict[str, BaseDescriptor] = {}
        
        # 执行历史
        self.execution_history: List[Dict[str, Any]] = []
        
        logger.info("AI执行引擎初始化完成")
    
    def register_descriptor(self, descriptor: BaseDescriptor):
        """
        注册描述器
        
        Args:
            descriptor: 描述器实例
        """
        self.descriptors[descriptor.name] = descriptor
        logger.info(f"注册描述器: {descriptor.name} (类型: {descriptor.step_type.value})")
    
    def unregister_descriptor(self, name: str):
        """
        注销描述器
        
        Args:
            name: 描述器名称
        """
        if name in self.descriptors:
            del self.descriptors[name]
            logger.info(f"注销描述器: {name}")
    
    def get_descriptors_by_type(self, step_type: StepType) -> List[BaseDescriptor]:
        """
        根据步骤类型获取描述器
        
        Args:
            step_type: 步骤类型
            
        Returns:
            List[BaseDescriptor]: 描述器列表
        """
        return [
            descriptor for descriptor in self.descriptors.values()
            if descriptor.step_type == step_type
        ]
    
    def get_applicable_descriptors(self, context: ExecutionContext) -> List[BaseDescriptor]:
        """
        获取适用的描述器
        
        Args:
            context: 执行上下文
            
        Returns:
            List[BaseDescriptor]: 适用的描述器列表
        """
        applicable = []
        for descriptor in self.descriptors.values():
            if descriptor.is_applicable(context):
                applicable.append(descriptor)
        
        # 按适用性分数排序
        applicable.sort(
            key=lambda d: d.get_applicability_score(context),
            reverse=True
        )
        
        return applicable
    
    async def execute_single_descriptor(
        self,
        descriptor_name: str,
        context: ExecutionContext,
        model_name: str = "default",
        provider_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        执行单个描述器
        
        Args:
            descriptor_name: 描述器名称
            context: 执行上下文
            model_name: 使用的模型名称
            provider_name: 模型提供商名称，如果为None则使用默认提供商
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        if descriptor_name not in self.descriptors:
            raise ValueError(f"描述器 {descriptor_name} 未注册")
        
        descriptor = self.descriptors[descriptor_name]
        
        try:
            logger.info(f"开始执行描述器: {descriptor_name}")
            
            # 执行描述器
            result = await self.descriptor_executor.execute_descriptor(
                descriptor, 
                context, 
                model_name,
                provider_name
            )
            
            # 记录执行历史
            self._record_execution(descriptor_name, context, result)
            
            return result
            
        except Exception as e:
            logger.error(f"描述器 {descriptor_name} 执行失败: {str(e)}")
            raise
    
    async def execute_tool_instructions(
        self,
        instructions: List[ToolInstruction],
        context: ExecutionContext
    ) -> Dict[str, Any]:
        """
        执行工具指令
        
        Args:
            instructions: 工具指令列表
            context: 执行上下文
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        try:
            logger.info(f"开始执行 {len(instructions)} 个工具指令")
            
            result = await self.tool_manager.execute_instructions(instructions, context)
            
            # 记录执行历史
            self._record_execution("tool_instructions", context, result)
            
            return result
            
        except Exception as e:
            logger.error(f"工具指令执行失败: {str(e)}")
            raise
    
    async def execute_workflow(
        self,
        descriptors: List[BaseDescriptor],
        context: ExecutionContext,
        dependencies: Optional[Dict[str, List[str]]] = None,
        max_parallel: int = 3,
        fail_fast: bool = False,
        model_name: str = "default",
        provider_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        执行工作流
        
        Args:
            descriptors: 描述器列表
            context: 执行上下文
            dependencies: 步骤依赖关系 {步骤名: [依赖步骤列表]}
            max_parallel: 最大并行数
            fail_fast: 是否快速失败
            model_name: 使用的模型名称
            provider_name: 模型提供商名称，如果为None则使用默认提供商
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        try:
            # 重置工作流编排器
            self.workflow_orchestrator.reset()
            
            # 添加步骤
            for descriptor in descriptors:
                step_dependencies = dependencies.get(descriptor.name, []) if dependencies else []
                self.workflow_orchestrator.add_step(
                    descriptor.name,
                    descriptor,
                    step_dependencies
                )
            
            logger.info(f"开始执行工作流，包含 {len(descriptors)} 个步骤")
            
            # 执行工作流
            result = await self.workflow_orchestrator.execute_workflow(
                context,
                max_parallel=max_parallel,
                fail_fast=fail_fast,
                model_name=model_name,
                provider_name=provider_name
            )
            
            # 记录执行历史
            self._record_execution("workflow", context, result)
            
            return result
            
        except Exception as e:
            logger.error(f"工作流执行失败: {str(e)}")
            raise
    
    async def auto_execute(
        self,
        context: ExecutionContext,
        step_types: Optional[List[StepType]] = None,
        max_descriptors_per_type: int = 3,
        model_name: str = "default",
        provider_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        自动执行（根据上下文自动选择合适的描述器）
        
        Args:
            context: 执行上下文
            step_types: 要执行的步骤类型列表，None表示所有类型
            max_descriptors_per_type: 每种类型最多执行的描述器数量
            model_name: 使用的模型名称
            provider_name: 模型提供商名称，如果为None则使用默认提供商
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        try:
            # 获取适用的描述器
            applicable_descriptors = self.get_applicable_descriptors(context)
            
            if not applicable_descriptors:
                return {
                    "success": True,
                    "message": "没有找到适用的描述器",
                    "results": {}
                }
            
            # 按步骤类型分组
            descriptors_by_type = {}
            for descriptor in applicable_descriptors:
                step_type = descriptor.step_type
                
                # 如果指定了步骤类型，只处理指定的类型
                if step_types and step_type not in step_types:
                    continue
                
                if step_type not in descriptors_by_type:
                    descriptors_by_type[step_type] = []
                
                # 限制每种类型的描述器数量
                if len(descriptors_by_type[step_type]) < max_descriptors_per_type:
                    descriptors_by_type[step_type].append(descriptor)
            
            # 构建执行列表
            execution_descriptors = []
            for descriptors in descriptors_by_type.values():
                execution_descriptors.extend(descriptors)
            
            if not execution_descriptors:
                return {
                    "success": True,
                    "message": "没有符合条件的描述器",
                    "results": {}
                }
            
            logger.info(f"自动执行模式，选择了 {len(execution_descriptors)} 个描述器")
            
            # 执行工作流
            return await self.execute_workflow(
                execution_descriptors,
                context,
                model_name=model_name,
                provider_name=provider_name
            )
            
        except Exception as e:
            logger.error(f"自动执行失败: {str(e)}")
            raise
    
    def _record_execution(
        self,
        execution_type: str,
        context: ExecutionContext,
        result: Dict[str, Any]
    ):
        """
        记录执行历史
        
        Args:
            execution_type: 执行类型
            context: 执行上下文
            result: 执行结果
        """
        record = {
            "id": str(uuid4()),
            "execution_type": execution_type,
            "timestamp": datetime.now().isoformat(),
            "process_id": context.process_id,
            "process_name": context.process_name,
            "success": result.get("success", True),
            "result_summary": {
                "total": result.get("total", 1),
                "completed": result.get("completed", 1 if result.get("success", True) else 0),
                "errors": len(result.get("errors", []))
            }
        }
        
        self.execution_history.append(record)
        
        # 保持历史记录数量在合理范围内
        if len(self.execution_history) > 1000:
            self.execution_history = self.execution_history[-500:]
    
    def get_execution_history(
        self,
        limit: int = 100,
        process_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        获取执行历史
        
        Args:
            limit: 返回记录数量限制
            process_id: 过滤特定流程ID
            
        Returns:
            List[Dict[str, Any]]: 执行历史记录
        """
        history = self.execution_history
        
        if process_id:
            history = [
                record for record in history
                if record.get("process_id") == process_id
            ]
        
        # 按时间倒序排列
        history = sorted(history, key=lambda x: x["timestamp"], reverse=True)
        
        return history[:limit]
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取引擎状态
        
        Returns:
            Dict[str, Any]: 引擎状态信息
        """
        return {
            "registered_descriptors": len(self.descriptors),
            "descriptors_by_type": {
                step_type.value: len(self.get_descriptors_by_type(step_type))
                for step_type in StepType
            },
            "execution_history_count": len(self.execution_history),
            "workflow_status": self.workflow_orchestrator.get_execution_status()
        }
