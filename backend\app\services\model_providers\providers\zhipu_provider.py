"""
智谱GLM提供商
"""

from typing import List
import structlog

from ..base import BaseModelProvider
from ..models import (
    ModelProviderType,
    ModelConfig,
    ModelCapability,
    ModelStatus
)

logger = structlog.get_logger(__name__)


class ZhipuProvider(BaseModelProvider):
    """智谱GLM提供商"""
    
    async def _initialize(self) -> None:
        """初始化智谱提供商"""
        # 验证API密钥
        if not self.config.api_key:
            raise ValueError("智谱API密钥未配置")
        
        # 设置默认API基础URL
        if not self.config.api_base:
            self.config.api_base = "https://open.bigmodel.cn"
        
        logger.info(f"智谱提供商初始化完成，API基础URL: {self.config.api_base}")
    
    async def _get_models(self) -> List[ModelConfig]:
        """获取智谱模型列表"""
        # 只从配置中获取模型，不使用硬编码
        if not self.config.models:
            logger.warning("智谱提供商配置中没有定义模型")
            return []

        return self.config.models.copy()
    
    async def _validate_model(self, model: ModelConfig) -> bool:
        """验证智谱模型"""
        # 这里可以实现具体的模型验证逻辑
        # 例如发送一个简单的请求来测试模型是否可用
        return model.status == ModelStatus.AVAILABLE
