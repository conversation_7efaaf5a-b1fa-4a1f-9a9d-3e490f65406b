"""
工具处理器基类

定义工具处理器的标准接口和行为
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Type
from pydantic import BaseModel

from ..models.context import ExecutionContext

logger = logging.getLogger(__name__)


class BaseToolHandler(ABC):
    """工具处理器基类"""

    def __init__(self, tool_name: str):
        self.tool_name = tool_name
    
    @abstractmethod
    async def execute(self, parameters: Dict[str, Any], context: ExecutionContext) -> Any:
        """
        执行工具调用

        Args:
            parameters: 工具参数
            context: 执行上下文

        Returns:
            Any: 执行结果
        """
        pass

    @abstractmethod
    def get_args_schema(self) -> Type[BaseModel]:
        """
        获取工具参数的Pydantic模型定义

        Returns:
            Type[BaseModel]: 参数模型类
        """
        pass
    
    def validate_parameters(self, parameters: Dict[str, Any]) -> List[str]:
        """
        验证参数
        
        Args:
            parameters: 工具参数
            
        Returns:
            List[str]: 验证错误列表
        """
        return []
    
    def get_tool_name(self) -> str:
        """
        获取工具名称

        Returns:
            str: 工具名称
        """
        return self.tool_name

    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.__class__.__name__}(tool_name={self.tool_name})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return f"{self.__class__.__name__}(tool_name={self.tool_name})"
