# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/
.coverage
htmlcov/
.tox/
.venv
venv/
ENV/

# 配置文件
!.env
!.env.*
!.env.example

.env.production

# Node
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.env.local
.env.*.local

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store
*.sublime-project
*.sublime-workspace

# 系统文件
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini

# 日志文件
*.log
logs/
log/

# 数据库
*.sqlite3
*.db

# 临时文件
*.tmp
*.temp
.temp/
tmp/

# Docker
.docker/
docker-compose.override.yml

# 其他
.history/
.cache/
.local/
*.bak
*.orig
logstash/data/
.augment/
*/_state
elasticsearch/data/
data/
/volumes/
