"""
工具封装器

将执行框架中的工具描述符封装为LangChain的BaseTool，供AgentExecutor调用
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Type, Union
from pydantic import BaseModel, Field

from langchain_core.tools import BaseTool
from langchain_core.tools import ToolException
from langchain_core.callbacks import CallbackManagerForToolRun, AsyncCallbackManagerForToolRun

from ..base.descriptor import BaseDescriptor
from ..models.context import ExecutionContext
from ..models.tool_instruction import ToolInstruction
from ..tools.tool_manager import ToolManager

logger = logging.getLogger(__name__)


class DefaultToolInput(BaseModel):
    """默认工具输入参数"""
    parameters: Dict[str, Any] = Field(description="工具调用参数")


class DescriptorToolWrapper(BaseTool):
    """描述符工具封装器

    将BaseDescriptor中的工具指令封装为LangChain的BaseTool
    """

    name: str = Field(description="工具名称")
    description: str = Field(description="工具描述")
    args_schema: Type[BaseModel] = Field(description="参数模型")

    # 添加自定义字段
    tool_instruction: ToolInstruction = Field(description="工具指令")
    tool_manager: ToolManager = Field(description="工具管理器")
    context: ExecutionContext = Field(description="执行上下文")

    model_config = {"arbitrary_types_allowed": True}
    
    def __init__(
        self,
        tool_instruction: ToolInstruction,
        tool_manager: ToolManager,
        context: ExecutionContext,
        **kwargs
    ):
        """
        初始化描述符工具封装器

        Args:
            tool_instruction: 工具指令
            tool_manager: 工具管理器
            context: 执行上下文
        """
        # 从工具管理器获取参数模型
        args_schema = tool_manager.get_tool_args_schema(tool_instruction.name)
        if not args_schema:
            # 如果没有找到对应的参数模型，使用默认的通用模型
            args_schema = DefaultToolInput

        super().__init__(
            name=tool_instruction.name,
            description=tool_instruction.description,
            args_schema=args_schema,
            tool_instruction=tool_instruction,
            tool_manager=tool_manager,
            context=context,
            **kwargs
        )
    
    def _run(
        self,
        **kwargs
    ) -> str:
        """同步运行工具"""
        return asyncio.run(self._arun(**kwargs))
    
    async def _arun(
        self,
        run_manager: Optional[AsyncCallbackManagerForToolRun] = None,
        **kwargs
    ) -> str:
        """异步运行工具"""
        try:
            # 从kwargs中提取参数，过滤掉run_manager
            parameters = {k: v for k, v in kwargs.items() if k != 'run_manager'}

            # 更新工具指令参数
            updated_instruction = ToolInstruction(
                name=self.tool_instruction.name,
                parameters={**self.tool_instruction.parameters, **parameters},
                description=self.tool_instruction.description,
                required=self.tool_instruction.required,
                order=self.tool_instruction.order,
                condition=self.tool_instruction.condition,
                result_key=self.tool_instruction.result_key,
                depends_on=self.tool_instruction.depends_on
            )

            # 执行工具指令
            result = await self.tool_manager.execute_instruction(updated_instruction, self.context)

            if result.get("success", False):
                return str(result.get("result", ""))
            else:
                raise ToolException(f"工具执行失败: {result.get('error', '未知错误')}")

        except Exception as e:
            logger.error(f"工具 {self.name} 执行失败: {str(e)}")
            raise ToolException(f"工具执行失败: {str(e)}")


class ExecutionFrameworkToolWrapper:
    """执行框架工具封装器

    将BaseDescriptor中的所有工具指令封装为LangChain的BaseTool列表
    """

    def __init__(self, tool_manager: Optional[ToolManager] = None):
        """
        初始化执行框架工具封装器

        Args:
            tool_manager: 工具管理器，如果为None则创建新实例
        """
        self.tool_manager = tool_manager or ToolManager()
    
    def wrap_descriptor_tools(
        self,
        descriptor: BaseDescriptor,
        context: ExecutionContext
    ) -> List[BaseTool]:
        """
        封装描述符的工具指令为BaseTool列表
        
        Args:
            descriptor: 描述符实例
            context: 执行上下文
            
        Returns:
            List[BaseTool]: 封装后的工具列表
        """
        try:
            # 获取工具指令列表
            tool_instructions = descriptor.get_tool_instructions(context)
            
            if not tool_instructions:
                logger.info(f"描述符 {descriptor.name} 没有工具指令")
                return []
            
            wrapped_tools = []
            
            for instruction in tool_instructions:
                try:
                    # 创建工具封装器
                    tool_wrapper = DescriptorToolWrapper(
                        tool_instruction=instruction,
                        tool_manager=self.tool_manager,
                        context=context
                    )
                    
                    wrapped_tools.append(tool_wrapper)
                    logger.info(f"成功封装工具: {instruction.name}")
                    
                except Exception as e:
                    logger.error(f"封装工具 {instruction.name} 失败: {str(e)}")
                    continue

            logger.info(f"描述符 {descriptor.name} 共封装了 {len(wrapped_tools)} 个工具")
            return wrapped_tools
            
        except Exception as e:
            logger.error(f"封装描述符 {descriptor.name} 的工具失败: {str(e)}")
            return []
    
    def wrap_multiple_descriptors(
        self,
        descriptors: List[BaseDescriptor],
        context: ExecutionContext
    ) -> List[BaseTool]:
        """
        封装多个描述符的工具指令为BaseTool列表
        
        Args:
            descriptors: 描述符列表
            context: 执行上下文
            
        Returns:
            List[BaseTool]: 封装后的工具列表
        """
        all_tools = []
        
        for descriptor in descriptors:
            tools = self.wrap_descriptor_tools(descriptor, context)
            all_tools.extend(tools)
        
        logger.info(f"总共封装了 {len(all_tools)} 个工具")
        return all_tools



