"""
描述器配置模型

定义描述器使用的配置结构，包含提示词配置和输出格式
"""

from typing import List, Dict, Optional, Any
from pydantic import BaseModel, Field


class DescriptorConfig(BaseModel):
    """描述器配置 - 专注于提示词相关配置"""

    template: str = Field(..., description="描述模板")
    input_variables: List[str] = Field(..., description="输入变量列表")
    system_prompt: Optional[str] = Field(default=None, description="系统提示词")
    examples: List[Dict[str, str]] = Field(default_factory=list, description="示例列表")
    constraints: List[str] = Field(default_factory=list, description="约束条件")
    output_format: Optional[str] = Field(default=None, description="输出格式要求")
    
    def validate_input_variables(self, variables: Dict[str, Any]) -> List[str]:
        """
        验证输入变量是否完整
        
        Args:
            variables: 输入变量字典
            
        Returns:
            List[str]: 缺少的变量列表
        """
        missing = []
        for var in self.input_variables:
            if var not in variables:
                missing.append(var)
        return missing
    
    def get_formatted_template(self, variables: Dict[str, Any]) -> str:
        """
        获取格式化的模板
        
        Args:
            variables: 变量字典
            
        Returns:
            str: 格式化后的模板
        """
        try:
            return self.template.format(**variables)
        except KeyError as e:
            # 如果有缺少的变量，用占位符替换
            safe_variables = variables.copy()
            for var in self.input_variables:
                if var not in safe_variables:
                    safe_variables[var] = f"[{var}]"
            return self.template.format(**safe_variables)
    
    def get_full_prompt(self, context: Dict[str, Any]) -> str:
        """
        获取完整的提示词（包含系统提示词）
        
        Args:
            context: 上下文数据
            
        Returns:
            str: 完整提示词
        """
        parts = []
        
        if self.system_prompt:
            parts.append(f"系统提示: {self.system_prompt}")
        
        if self.examples:
            parts.append("示例:")
            for i, example in enumerate(self.examples, 1):
                parts.append(f"示例{i}:")
                for key, value in example.items():
                    parts.append(f"{key}: {value}")
        
        if self.constraints:
            parts.append("约束条件:")
            for constraint in self.constraints:
                parts.append(f"- {constraint}")
        
        parts.append(self.get_formatted_template(context))
        
        if self.output_format:
            parts.append(f"输出格式: {self.output_format}")
        
        return "\n\n".join(parts)


class DescriptorConfigBuilder:
    """描述器配置构建器"""
    
    @staticmethod
    def create_basic_config(
        template: str,
        input_variables: List[str],
        system_prompt: Optional[str] = None
    ) -> DescriptorConfig:
        """创建基础配置"""
        return DescriptorConfig(
            template=template,
            input_variables=input_variables,
            system_prompt=system_prompt
        )
    
    @staticmethod
    def create_with_examples(
        template: str,
        input_variables: List[str],
        examples: List[Dict[str, str]],
        system_prompt: Optional[str] = None
    ) -> DescriptorConfig:
        """创建带示例的配置"""
        return DescriptorConfig(
            template=template,
            input_variables=input_variables,
            examples=examples,
            system_prompt=system_prompt
        )
    
    @staticmethod
    def create_with_constraints(
        template: str,
        input_variables: List[str],
        constraints: List[str],
        system_prompt: Optional[str] = None
    ) -> DescriptorConfig:
        """创建带约束的配置"""
        return DescriptorConfig(
            template=template,
            input_variables=input_variables,
            constraints=constraints,
            system_prompt=system_prompt
        )
    
    @staticmethod
    def create_full_config(
        template: str,
        input_variables: List[str],
        system_prompt: Optional[str] = None,
        examples: Optional[List[Dict[str, str]]] = None,
        constraints: Optional[List[str]] = None,
        output_format: Optional[str] = None
    ) -> DescriptorConfig:
        """创建完整配置"""
        return DescriptorConfig(
            template=template,
            input_variables=input_variables,
            system_prompt=system_prompt,
            examples=examples or [],
            constraints=constraints or [],
            output_format=output_format
        )
