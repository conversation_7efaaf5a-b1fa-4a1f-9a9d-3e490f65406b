"""
介绍生成描述器

通过提示词指导LLM生成项目介绍和快速上手指南
"""

import logging
from typing import List

from ...execution_framework.base.step_type import StepType
from ...execution_framework.models.context import ExecutionContext
from ...execution_framework.models.tool_instruction import ToolInstruction
from .base_generation_descriptor import BaseGenerationDescriptor

logger = logging.getLogger(__name__)


class IntroductionDescriptor(BaseGenerationDescriptor):
    """项目介绍生成描述器 - 指导LLM生成项目介绍"""

    def __init__(self):
        """初始化介绍生成描述器"""
        super().__init__(StepType.INTRODUCTION, "introduction")

    def get_description(self) -> str:
        """获取描述器描述"""
        return "指导LLM生成项目介绍和快速上手指南"

    def get_tool_instructions(self, context: ExecutionContext) -> List[ToolInstruction]:
        """获取工具调用指令列表 - 每个工具只定义一次"""
        return [
            ToolInstruction(
                name="codebase-retrieval",
                parameters={
                    "information_request": "项目核心功能、技术特点、使用场景、主要优势"
                },
                description="获取项目核心信息用于生成介绍",
                result_key="codebase_analysis"
            ),
            ToolInstruction(
                name="view-file",
                parameters={"path": "package.json"},
                description="查看项目配置获取基本信息",
                result_key="project_config"
            )
        ]

    def get_prompt_template(self) -> str:
        """获取提示词模板 - 指导LLM如何生成项目介绍"""
        return """
## 项目信息
项目名称: {project_name}
项目路径: {project_path}
项目类型: {project_type}
编程语言: {programming_languages}
技术框架: {frameworks}

## 代码库分析
{codebase_analysis}

## 项目配置
{project_config}

## 自定义要求
{custom_prompt}

## 生成任务

请基于以上项目信息，使用可用工具（如 codebase-retrieval、view-file）核实事实后，生成项目介绍与快速开始内容。
"""

    def get_input_variables(self) -> List[str]:
        """获取输入变量列表"""
        return [
            "project_name",
            "project_path",
            "project_type",
            "programming_languages",
            "frameworks",
            "codebase_analysis",
            "project_config",
            "custom_prompt"
        ]

    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return "你是一个专业的技术文档撰写专家，擅长创作引人入胜的项目介绍。"

    def get_constraints(self) -> List[str]:
        """将写作要求迁移为约束条件"""
        return [
            "使用Markdown格式",
            "语言专业但易懂，突出项目价值和特色",
            "内容必须基于实际项目信息（结合代码库分析与配置）",
            "输出包含：项目概述、核心特性（3-6条）、快速开始（环境要求、2-3步安装、最简单示例、文档链接）",
            "总长度控制在800-1200字",
        ]

    def get_priority(self, context: ExecutionContext) -> int:
        """获取优先级 - 介绍部分优先级高"""
        return 100

    def get_dependencies(self) -> List[str]:
        """获取依赖 - 可能依赖逻辑树规划"""
        return ["logic_tree"]
