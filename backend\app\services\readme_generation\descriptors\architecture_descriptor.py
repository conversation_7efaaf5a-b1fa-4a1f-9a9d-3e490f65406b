"""
架构说明生成描述器

通过提示词指导LLM生成项目架构设计和技术选型说明
"""

import logging
from typing import List

from ...execution_framework.base.step_type import StepType
from ...execution_framework.models.context import ExecutionContext
from ...execution_framework.models.tool_instruction import ToolInstruction
from .base_generation_descriptor import BaseGenerationDescriptor

logger = logging.getLogger(__name__)


class ArchitectureDescriptor(BaseGenerationDescriptor):
    """项目架构说明生成描述器 - 指导LLM生成架构设计文档"""

    def __init__(self):
        """初始化架构说明生成描述器"""
        super().__init__(StepType.ARCHITECTURE, "architecture")

    def get_description(self) -> str:
        """获取描述器描述"""
        return "指导LLM生成项目架构设计、技术选型和系统组件说明"

    def get_tool_instructions(self, context: ExecutionContext) -> List[ToolInstruction]:
        """获取工具调用指令列表 - 每个工具只定义一次"""
        return [
            ToolInstruction(
                name="codebase-retrieval",
                parameters={
                    "information_request": "项目架构设计、模块划分、技术选型、设计模式、系统组件、数据流"
                },
                description="获取项目架构相关信息",
                result_key="codebase_analysis"
            ),
            ToolInstruction(
                name="view-directory",
                parameters={"path": "."},
                description="查看项目整体结构",
                result_key="directory_structure"
            ),
            ToolInstruction(
                name="view-file",
                parameters={"path": "architecture.md"},
                description="查看现有架构文档",
                result_key="existing_architecture_doc"
            )
        ]

    def get_prompt_template(self) -> str:
        """获取提示词模板 - 指导LLM如何生成架构说明"""
        return """
## 项目信息
项目名称: {project_name}
项目路径: {project_path}
项目类型: {project_type}
编程语言: {programming_languages}
技术框架: {frameworks}

## 代码库分析
{codebase_analysis}

## 项目结构
{directory_structure}

## 现有架构文档
{existing_architecture_doc}

## 自定义要求
{custom_prompt}

## 生成任务

请基于以上项目信息，使用可用工具（如 codebase-retrieval、view-directory）深入分析后，生成项目架构说明。

重点包含：
1. 整体架构设计和核心理念
2. 系统模块划分和职责
3. 技术选型和设计决策
4. 数据流和交互关系
5. 扩展性和可维护性考虑
"""

    def get_input_variables(self) -> List[str]:
        """获取输入变量列表"""
        return [
            "project_name",
            "project_path",
            "project_type",
            "programming_languages",
            "frameworks",
            "codebase_analysis",
            "directory_structure",
            "existing_architecture_doc",
            "custom_prompt"
        ]

    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return "你是一个资深的软件架构师和系统设计专家，擅长分析和阐述复杂系统的架构设计。"

    def get_constraints(self) -> List[str]:
        """将写作要求迁移为约束条件"""
        return [
            "使用Markdown格式",
            "提供清晰的架构图表描述（用文字描述图表内容）",
            "内容必须基于实际项目代码结构和设计",
            "输出包含：架构概览、模块设计、技术选型、数据流、设计原则",
            "用专业但易懂的语言解释技术决策",
            "突出架构的优势和特色",
            "总长度控制在1000-1500字",
        ]

    def get_priority(self, context: ExecutionContext) -> int:
        """获取优先级 - 架构说明优先级中等"""
        return 70

    def get_dependencies(self) -> List[str]:
        """获取依赖 - 依赖功能特性部分"""
        return ["features"]
