"""
模型提供商数据模型
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field


class ModelProviderType(str, Enum):
    """模型提供商类型"""
    OPENAI = "openai"
    GOOGLE = "google"
    ANTHROPIC = "anthropic"
    OPENROUTER = "openrouter"
    OLLAMA = "ollama"
    LOCAL = "local"
    AZURE_OPENAI = "azure_openai"
    HUGGINGFACE = "huggingface"

    # 国内提供商
    BAIDU = "baidu"           # 百度文心一言
    ALIBABA = "alibaba"       # 阿里通义千问
    TENCENT = "tencent"       # 腾讯混元
    ZHIPU = "zhipu"          # 智谱GLM
    MOONSHOT = "moonshot"     # 月之暗面Kimi
    DEEPSEEK = "deepseek"     # DeepSeek
    MINIMAX = "minimax"       # MiniMax
    XUNFEI = "xunfei"        # 讯飞星火


class ModelCapability(str, Enum):
    """模型能力类型"""
    TEXT_GENERATION = "text_generation"
    CHAT = "chat"
    CODE_GENERATION = "code_generation"
    EMBEDDING = "embedding"
    IMAGE_GENERATION = "image_generation"
    IMAGE_UNDERSTANDING = "image_understanding"
    VISION = "vision"
    AUDIO = "audio"
    FUNCTION_CALLING = "function_calling"
    STREAMING = "streaming"
    JSON_MODE = "json_mode"


class ModelStatus(str, Enum):
    """模型状态"""
    AVAILABLE = "available"
    UNAVAILABLE = "unavailable"
    DEPRECATED = "deprecated"
    BETA = "beta"
    UNKNOWN = "unknown"


class ModelConfig(BaseModel):
    """模型配置"""
    name: str = Field(description="模型名称")
    display_name: Optional[str] = Field(default=None, description="显示名称")
    provider: ModelProviderType = Field(description="模型提供商")
    model_id: Optional[str] = Field(default=None, description="模型ID，如果与name不同")

    # 连接配置
    api_key: Optional[str] = Field(default=None, description="API密钥")
    secret_key: Optional[str] = Field(default=None, description="API密钥")
    api_base: Optional[str] = Field(default=None, description="API基础URL")
    proxy: Optional[str] = Field(default=None, description="代理URL")

    # 基础配置
    max_tokens: Optional[int] = Field(default=None, description="最大token数")
    context_length: Optional[int] = Field(default=None, description="上下文长度")
    temperature: float = Field(default=0.7, description="温度参数")
    top_p: float = Field(default=1.0, description="Top-p参数")
    top_k: Optional[int] = Field(default=None, description="Top-k参数")
    
    # 能力配置
    capabilities: List[ModelCapability] = Field(default_factory=list, description="支持的能力")
    supports_streaming: bool = Field(default=False, description="是否支持流式输出")
    supports_function_calling: bool = Field(default=False, description="是否支持函数调用")
    supports_json_mode: bool = Field(default=False, description="是否支持JSON模式")
    supports_vision: bool = Field(default=False, description="是否支持视觉理解")
    supports_audio: bool = Field(default=False, description="是否支持音频处理")

    # 成本配置
    input_cost_per_token: Optional[float] = Field(default=None, description="输入token成本")
    output_cost_per_token: Optional[float] = Field(default=None, description="输出token成本")
    
    # 状态信息
    status: ModelStatus = Field(default=ModelStatus.UNKNOWN, description="模型状态")
    description: Optional[str] = Field(default=None, description="模型描述")
    
    # 额外参数
    extra_params: Dict[str, Any] = Field(default_factory=dict, description="额外参数")
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")


class ModelProviderConfig(BaseModel):
    """模型提供商配置"""
    provider: ModelProviderType = Field(description="提供商类型")
    name: str = Field(description="提供商名称")
    
    # 连接配置
    api_key: Optional[str] = Field(default=None, description="API密钥")
    secret_key: Optional[str] = Field(default=None, description="API密钥")
    api_base: Optional[str] = Field(default=None, description="API基础URL")
    api_version: Optional[str] = Field(default=None, description="API版本")
    
    # 请求配置
    timeout: int = Field(default=30, description="请求超时时间（秒）")
    max_retries: int = Field(default=3, description="最大重试次数")
    retry_delay: float = Field(default=1.0, description="重试延迟（秒）")
    
    # 代理配置
    proxy: Optional[str] = Field(default=None, description="代理URL")
    
    # 模型列表
    models: List[ModelConfig] = Field(default_factory=list, description="支持的模型列表")
    
    # 默认模型
    default_model: Optional[str] = Field(default=None, description="默认模型名称")
    
    # 额外配置
    extra_headers: Dict[str, str] = Field(default_factory=dict, description="额外请求头")
    extra_params: Dict[str, Any] = Field(default_factory=dict, description="额外参数")
    
    # 状态信息
    enabled: bool = Field(default=True, description="是否启用")
    description: Optional[str] = Field(default=None, description="提供商描述")
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")


class ModelProviderStatus(BaseModel):
    """模型提供商状态"""
    provider: ModelProviderType = Field(description="提供商类型")
    name: str = Field(description="提供商名称")
    is_available: bool = Field(description="是否可用")
    is_connected: bool = Field(description="是否已连接")
    last_error: Optional[str] = Field(default=None, description="最后错误信息")
    
    # 模型统计
    total_models: int = Field(default=0, description="总模型数")
    available_models: int = Field(default=0, description="可用模型数")
    
    # 操作统计
    total_requests: int = Field(default=0, description="总请求数")
    successful_requests: int = Field(default=0, description="成功请求数")
    failed_requests: int = Field(default=0, description="失败请求数")
    
    # 性能统计
    average_response_time: float = Field(default=0.0, description="平均响应时间（秒）")
    last_request_time: Optional[datetime] = Field(default=None, description="最后请求时间")
    
    # 时间戳
    checked_at: datetime = Field(default_factory=datetime.now, description="检查时间")


class ModelSwitchRequest(BaseModel):
    """模型切换请求"""
    provider: Optional[str] = Field(default=None, description="提供商名称")
    model_name: str = Field(description="模型名称")
    reason: Optional[str] = Field(default=None, description="切换原因")


class ProviderModels(BaseModel):
    """提供商模型信息"""
    provider_name: str = Field(description="提供商名称")
    provider_type: ModelProviderType = Field(description="提供商类型")
    is_available: bool = Field(description="提供商是否可用")
    models: List[ModelConfig] = Field(description="该提供商的模型列表")
    model_count: int = Field(description="模型数量")


class ModelListResponse(BaseModel):
    """模型列表响应"""
    providers: List[ProviderModels] = Field(description="按提供商分类的模型列表")
    current_model: Optional[str] = Field(default=None, description="当前模型")
    current_provider: Optional[str] = Field(default=None, description="当前提供商")
    total_providers: int = Field(description="提供商总数")
    total_models: int = Field(description="模型总数")
