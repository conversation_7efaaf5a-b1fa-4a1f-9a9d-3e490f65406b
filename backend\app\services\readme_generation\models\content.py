"""
README节点内容模型

定义节点内容和版本管理
"""

import uuid
from enum import Enum
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
from pydantic import BaseModel, Field


class ContentStatus(str, Enum):
    """内容状态枚举"""
    DRAFT = "draft"  # 草稿
    GENERATED = "generated"  # 已生成
    REVIEWED = "reviewed"  # 已审核
    PUBLISHED = "published"  # 已发布
    ARCHIVED = "archived"  # 已归档


class ContentVersion(BaseModel):
    """内容版本"""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="版本唯一标识")
    version_number: str = Field(..., description="版本号")
    content: str = Field(..., description="内容文本")
    
    # 生成信息
    prompt_used: str = Field(default="", description="使用的提示词")
    model_name: str = Field(default="", description="使用的模型名称")
    provider_name: Optional[str] = Field(default=None, description="模型提供商名称")
    generation_config: Dict[str, Any] = Field(default_factory=dict, description="生成配置")
    
    # 状态和元数据
    status: ContentStatus = Field(default=ContentStatus.DRAFT, description="内容状态")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    created_by: str = Field(default="system", description="创建者")
    
    # 质量评估
    quality_score: Optional[float] = Field(default=None, description="质量评分")
    feedback: str = Field(default="", description="反馈信息")
    
    def update_status(self, new_status: ContentStatus) -> None:
        """更新状态"""
        self.status = new_status
    
    def set_quality_score(self, score: float, feedback: str = "") -> None:
        """设置质量评分"""
        self.quality_score = score
        self.feedback = feedback


class NodeContent(BaseModel):
    """节点内容"""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="内容唯一标识")
    node_id: str = Field(..., description="关联的节点ID")
    tree_id: str = Field(..., description="关联的逻辑树ID")
    
    # 版本管理
    versions: List[ContentVersion] = Field(default_factory=list, description="版本列表")
    current_version_id: Optional[str] = Field(default=None, description="当前版本ID")
    
    # 内容配置
    content_type: str = Field(default="markdown", description="内容类型")
    language: str = Field(default="zh-CN", description="内容语言")
    
    # 元数据
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    def add_version(
        self, 
        content: str, 
        prompt_used: str = "",
        model_name: str = "",
        provider_name: Optional[str] = None,
        generation_config: Optional[Dict[str, Any]] = None,
        created_by: str = "system"
    ) -> str:
        """添加新版本"""
        version_number = f"v{len(self.versions) + 1}.0"
        
        version = ContentVersion(
            version_number=version_number,
            content=content,
            prompt_used=prompt_used,
            model_name=model_name,
            provider_name=provider_name,
            generation_config=generation_config or {},
            created_by=created_by
        )
        
        self.versions.append(version)
        self.current_version_id = version.id
        self.updated_at = datetime.now(timezone.utc)
        
        return version.id
    
    def get_current_version(self) -> Optional[ContentVersion]:
        """获取当前版本"""
        if not self.current_version_id:
            return None
        
        for version in self.versions:
            if version.id == self.current_version_id:
                return version
        return None
    
    def get_version(self, version_id: str) -> Optional[ContentVersion]:
        """获取指定版本"""
        for version in self.versions:
            if version.id == version_id:
                return version
        return None
    
    def set_current_version(self, version_id: str) -> bool:
        """设置当前版本"""
        for version in self.versions:
            if version.id == version_id:
                self.current_version_id = version_id
                self.updated_at = datetime.now(timezone.utc)
                return True
        return False
    
    def get_current_content(self) -> str:
        """获取当前内容"""
        current_version = self.get_current_version()
        return current_version.content if current_version else ""
    
    def get_version_history(self) -> List[Dict[str, Any]]:
        """获取版本历史"""
        history = []
        for version in sorted(self.versions, key=lambda v: v.created_at, reverse=True):
            history.append({
                "id": version.id,
                "version_number": version.version_number,
                "status": version.status,
                "created_at": version.created_at,
                "created_by": version.created_by,
                "model_name": version.model_name,
                "quality_score": version.quality_score,
                "is_current": version.id == self.current_version_id
            })
        return history
    
    def remove_version(self, version_id: str) -> bool:
        """删除版本"""
        if len(self.versions) <= 1:
            return False  # 至少保留一个版本
        
        for i, version in enumerate(self.versions):
            if version.id == version_id:
                # 如果删除的是当前版本，切换到最新版本
                if self.current_version_id == version_id:
                    remaining_versions = [v for v in self.versions if v.id != version_id]
                    if remaining_versions:
                        latest_version = max(remaining_versions, key=lambda v: v.created_at)
                        self.current_version_id = latest_version.id
                
                del self.versions[i]
                self.updated_at = datetime.now(timezone.utc)
                return True
        
        return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "total_versions": len(self.versions),
            "current_version": self.current_version_id,
            "content_length": len(self.get_current_content()),
            "last_updated": self.updated_at,
            "status_distribution": {
                status.value: len([v for v in self.versions if v.status == status])
                for status in ContentStatus
            }
        }
