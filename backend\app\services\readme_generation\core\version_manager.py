"""
版本管理器

管理节点内容的版本历史和版本操作
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone

from ..models.content import NodeContent, ContentVersion, ContentStatus
from ..storage.base import BaseStorage

logger = logging.getLogger(__name__)


class VersionManager:
    """版本管理器"""
    
    def __init__(self, storage: BaseStorage):
        """
        初始化版本管理器
        
        Args:
            storage: 存储实例
        """
        self.storage = storage
    
    async def create_version(
        self,
        node_content: NodeContent,
        content: str,
        prompt_used: str = "",
        model_name: str = "",
        provider_name: Optional[str] = None,
        generation_config: Optional[Dict[str, Any]] = None,
        created_by: str = "system"
    ) -> str:
        """
        创建新版本
        
        Args:
            node_content: 节点内容对象
            content: 内容文本
            prompt_used: 使用的提示词
            model_name: 使用的模型名称
            provider_name: 模型提供商名称
            generation_config: 生成配置
            created_by: 创建者
            
        Returns:
            str: 新版本ID
        """
        try:
            # 添加新版本
            version_id = node_content.add_version(
                content=content,
                prompt_used=prompt_used,
                model_name=model_name,
                provider_name=provider_name,
                generation_config=generation_config or {},
                created_by=created_by
            )
            
            # 保存到存储
            await self.storage.save_node_content(node_content)
            
            logger.info(f"创建新版本成功: {version_id}")
            return version_id
            
        except Exception as e:
            logger.error(f"创建版本失败: {e}")
            raise
    
    async def switch_version(
        self,
        node_content: NodeContent,
        version_id: str
    ) -> bool:
        """
        切换到指定版本
        
        Args:
            node_content: 节点内容对象
            version_id: 目标版本ID
            
        Returns:
            bool: 是否切换成功
        """
        try:
            # 切换版本
            success = node_content.set_current_version(version_id)
            
            if success:
                # 保存到存储
                await self.storage.save_node_content(node_content)
                logger.info(f"切换版本成功: {version_id}")
            else:
                logger.warning(f"版本不存在: {version_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"切换版本失败: {e}")
            return False
    
    async def delete_version(
        self,
        node_content: NodeContent,
        version_id: str
    ) -> bool:
        """
        删除指定版本
        
        Args:
            node_content: 节点内容对象
            version_id: 要删除的版本ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            # 删除版本
            success = node_content.remove_version(version_id)
            
            if success:
                # 保存到存储
                await self.storage.save_node_content(node_content)
                logger.info(f"删除版本成功: {version_id}")
            else:
                logger.warning(f"删除版本失败，版本可能不存在或是最后一个版本: {version_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"删除版本失败: {e}")
            return False
    
    async def update_version_status(
        self,
        node_content: NodeContent,
        version_id: str,
        new_status: ContentStatus
    ) -> bool:
        """
        更新版本状态
        
        Args:
            node_content: 节点内容对象
            version_id: 版本ID
            new_status: 新状态
            
        Returns:
            bool: 是否更新成功
        """
        try:
            version = node_content.get_version(version_id)
            if not version:
                logger.warning(f"版本不存在: {version_id}")
                return False
            
            # 更新状态
            version.update_status(new_status)
            
            # 保存到存储
            await self.storage.save_node_content(node_content)
            
            logger.info(f"更新版本状态成功: {version_id} -> {new_status}")
            return True
            
        except Exception as e:
            logger.error(f"更新版本状态失败: {e}")
            return False
    
    async def set_version_quality_score(
        self,
        node_content: NodeContent,
        version_id: str,
        score: float,
        feedback: str = ""
    ) -> bool:
        """
        设置版本质量评分
        
        Args:
            node_content: 节点内容对象
            version_id: 版本ID
            score: 质量评分
            feedback: 反馈信息
            
        Returns:
            bool: 是否设置成功
        """
        try:
            version = node_content.get_version(version_id)
            if not version:
                logger.warning(f"版本不存在: {version_id}")
                return False
            
            # 设置质量评分
            version.set_quality_score(score, feedback)
            
            # 保存到存储
            await self.storage.save_node_content(node_content)
            
            logger.info(f"设置版本质量评分成功: {version_id} -> {score}")
            return True
            
        except Exception as e:
            logger.error(f"设置版本质量评分失败: {e}")
            return False
    
    def get_version_history(self, node_content: NodeContent) -> List[Dict[str, Any]]:
        """
        获取版本历史
        
        Args:
            node_content: 节点内容对象
            
        Returns:
            List[Dict[str, Any]]: 版本历史列表
        """
        return node_content.get_version_history()
    
    def get_version_statistics(self, node_content: NodeContent) -> Dict[str, Any]:
        """
        获取版本统计信息
        
        Args:
            node_content: 节点内容对象
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        return node_content.get_statistics()
    
    def compare_versions(
        self,
        node_content: NodeContent,
        version_id1: str,
        version_id2: str
    ) -> Optional[Dict[str, Any]]:
        """
        比较两个版本
        
        Args:
            node_content: 节点内容对象
            version_id1: 版本1 ID
            version_id2: 版本2 ID
            
        Returns:
            Optional[Dict[str, Any]]: 比较结果
        """
        try:
            version1 = node_content.get_version(version_id1)
            version2 = node_content.get_version(version_id2)
            
            if not version1 or not version2:
                return None
            
            return {
                "version1": {
                    "id": version1.id,
                    "version_number": version1.version_number,
                    "content_length": len(version1.content),
                    "created_at": version1.created_at,
                    "model_name": version1.model_name,
                    "provider_name": version1.provider_name,
                    "quality_score": version1.quality_score
                },
                "version2": {
                    "id": version2.id,
                    "version_number": version2.version_number,
                    "content_length": len(version2.content),
                    "created_at": version2.created_at,
                    "model_name": version2.model_name,
                    "provider_name": version2.provider_name,
                    "quality_score": version2.quality_score
                },
                "differences": {
                    "content_length_diff": len(version2.content) - len(version1.content),
                    "time_diff": (version2.created_at - version1.created_at).total_seconds(),
                    "model_changed": version1.model_name != version2.model_name,
                    "provider_changed": version1.provider_name != version2.provider_name,
                    "quality_improved": (
                        version2.quality_score > version1.quality_score
                        if version1.quality_score and version2.quality_score
                        else None
                    )
                }
            }
            
        except Exception as e:
            logger.error(f"比较版本失败: {e}")
            return None
