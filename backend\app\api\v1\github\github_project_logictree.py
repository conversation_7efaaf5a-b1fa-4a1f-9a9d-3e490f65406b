#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/9/6 12:14
# @File    : github_project_logictree.py
# @Description: 逻辑树相关API处理器
"""

import structlog
from typing import List, Optional, Dict, Any
from tornado.web import HTTPError

from app.api.base import BaseHandler
from app.core.di.containers import Container
from app.schemas.github.github_project_logictree import (
    LogicTreeProjectCardsRequest, LogicTreeModelListRequest, 
    LogicTreeRewriteRequest, LogicTreeRewriteListRequest
)
from app.services.github.github_project_logictree import LogicTreeService
from dependency_injector.wiring import inject, Provide
from app.core.middleware import require_auth
from app.services.github.github_trending import GitHubTrendingService
from app.utils.security import get_current_user_id
# @GeneratedBy:AI - 添加 readme_generation_manager 导入
from app.services.readme_generation.generation_manager import GenerationManager

logger = structlog.get_logger(__name__)


# @require_auth(required=True)
class LogicTreeRewriteListHandler(BaseHandler):
    """逻辑树重写列表处理器"""

    @inject
    def initialize(
        self,
        logic_tree_service: LogicTreeService = Provide[Container.logic_tree_service]
    ):
        """初始化处理器"""
        self.logic_tree_service = logic_tree_service
        super().initialize()

    async def get(self) -> None:
        """获取用户重写列表"""
        try:
            # 获取当前用户ID
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)
            
            # 获取分页参数
            page = int(self.get_argument("page", "1"))
            page_size = int(self.get_argument("page_size", "10"))
            
            # 参数校验
            if page < 1:
                page = 1
            if page_size < 1 or page_size > 100:
                page_size = 10
            
            # 调用服务获取重写列表
            result = await self.logic_tree_service.get_user_rewrite_list(
                user_id=user_id,
                page=page,
                page_size=page_size
            )
            
            # 返回结果
            self.success_response({
                "items": result.items,
                "total": result.total,
                "page": result.page,
                "page_size": result.page_size
            })
            
        except Exception as e:
            logger.error("获取用户重写列表失败", error=str(e))
            self.write_error(500, error_message=f"获取用户重写列表失败: {str(e)}")


class LogicTreeProjectCardsHandler(BaseHandler):
    """逻辑树项目卡片处理器"""

    @inject
    def initialize(
        self,
        logic_tree_service: LogicTreeService = Provide[Container.logic_tree_service]
    ):
        """初始化处理器"""
        self.logic_tree_service = logic_tree_service
        super().initialize()

    async def get(self) -> None:
        """获取项目逻辑树"""
        try:
            # 获取参数
            project_id = self.get_argument("project_id", "")

            if not project_id:
                raise ValueError("project_id 不能为空")
            
            # 获取当前用户ID
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header) if auth_header else None
            
            # 调用服务获取逻辑树
            tree = await self.logic_tree_service.get_project_cards_tree_new(
                project_id=project_id,
                user_id=user_id
            )
            
            # 转换为字典格式
            # def tree_to_dict(card_tree):
            #     return {
            #         "card": card_tree.card.model_dump(),
            #         "children": [tree_to_dict(child) for child in card_tree.children]
            #     }

            # tmp
            def tree_to_dict(card_tree):
                result = {
                    "card": card_tree.card.model_dump(),
                    "history_keywords": card_tree.history_keywords,  # 添加历史关键词
                    "children": None  # 初始化为None
                }

                # 如果有子节点，递归转换（注意：children现在是单个对象，不是列表）
                if card_tree.children:
                    result["children"] = tree_to_dict(card_tree.children)

                return result

            tree_data = [tree_to_dict(card_tree) for card_tree in tree]
            
            # 返回结果
            self.success_response(tree_data)
            
        except Exception as e:
            logger.error("获取项目逻辑树失败", error=str(e))
            self.write_error(500, error_message=f"获取项目逻辑树失败: {str(e)}")


class LogicTreeModelListHandler(BaseHandler):
    """逻辑树模型列表处理器"""

    @inject
    def initialize(
        self,
        logic_tree_service: LogicTreeService = Provide[Container.logic_tree_service],
        readme_generation_manager: GenerationManager = Provide[Container.readme_generation_manager]

    ):
        """初始化处理器"""
        self.logic_tree_service = logic_tree_service,
        self.readme_generation_manager = readme_generation_manager
        super().initialize()

    async def get(self) -> None:
        """获取可用AI模型列表"""
        try:
            # 获取参数
            # project_id = self.get_argument("project_id", "")
            #
            # if not project_id:
            #     raise ValueError("project_id 不能为空")

            models = [
                "deepseek-reasoner",
                "gemini-2.5-flash"
            ]
            self.success_response(models)

            
        except Exception as e:
            logger.error("获取模型列表失败", error=str(e))
            self.write_error(500, error_message=f"获取模型列表失败: {str(e)}")


class LogicTreeRewriteHandler(BaseHandler):
    """逻辑树重写处理器"""

    @inject
    def initialize(
        self,
        logic_tree_service: LogicTreeService = Provide[Container.logic_tree_service],
        readme_generation_manager: GenerationManager = Provide[Container.readme_generation_manager],

    ):
        """初始化处理器"""
        super().initialize()
        self.logic_tree_service = logic_tree_service
        self.readme_generation_manager = readme_generation_manager

    async def post(self) -> None:
        """重写卡片"""
        try:
            # 获取当前用户ID
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)
            
            # 验证请求数据
            if not self.json_body:
                raise ValueError("请求体不能为空")
            
            # 获取参数
            card_id = self.json_body.get("card_id")
            prompt = self.json_body.get("prompt")
            # rewrite_text = self.json_body.get("rewrite_text")
            # model_name = self.json_body.get("model_name")
            #
            # if not all([card_id, prompt, model_name]):
            #     raise ValueError("card_id, project_id, prompt, model_name 不能为空")

            self.success_response({
                "message": "重写任务已开始，请稍后查看结果",
                "card_id": card_id,
                "status": "processing"
            })
            import asyncio
            # 在后台异步处理重写任务
            asyncio.create_task(self._process_rewrite_task(card_id, prompt, user_id))

            #
            # tree_id, node_id,project_id = await self.logic_tree_service.get_project_cards_node_and_tree(
            #     card_id=card_id
            # )
            #
            #
            # node_content = await self.readme_generation_manager.generate_node_content(
            #     tree_id=tree_id,
            #     node_id=node_id,
            #     custom_prompt=prompt,
            # #     model_name=model_name
            # )
            #
            #
            # # 调用服务重写卡片
            # new_card = await self.logic_tree_service.rewrite_card(
            #     card_id=card_id,
            #     project_id=project_id,
            #     prompt=prompt,
            #     model_name="model_name",
            #     user_id=user_id,
            #     rewrite_text="rewrite_text",
            #     node_content=node_content
            # )
            
            # 返回结果
            # self.success_response(new_card.model_dump())
            
        except ValueError as e:
            logger.error("重写卡片参数错误", error=str(e))
            self.write_error(500, error_message=f"重写卡片参数错误: {str(e)}")
        except Exception as e:
            logger.error("重写卡片失败", error=str(e))
            self.write_error(500, error_message=f"重写卡片失败: {str(e)}")

    async def _process_rewrite_task(self, card_id: str, prompt: str, user_id: str) -> None:
        """
        后台处理重写任务

        Args:
            card_id: 卡片ID
            prompt: 重写提示词
            user_id: 用户ID
        """
        try:
            logger.info(f"开始处理重写任务", card_id=card_id, user_id=user_id)

            # 获取项目信息
            tree_id, node_id, project_id = await self.logic_tree_service.get_project_cards_node_and_tree(
                card_id=card_id
            )

            # 生成节点内容
            node_result = await self.readme_generation_manager.generate_node(
                tree_id=tree_id,
                node_id=node_id,
                custom_prompt=prompt,
            )

            node_content = node_result.get("node_content")

            # 调用服务重写卡片
            new_card = await self.logic_tree_service.rewrite_card(
                card_id=card_id,
                project_id=project_id,
                prompt=prompt,
                model_name="default_model",  # 使用默认模型名称
                user_id=user_id,
                rewrite_text=node_content.get_current_content() if node_content else "",  # 使用生成的内容
                node_content=node_content
            )

            logger.info(f"重写任务完成", card_id=card_id, new_card_id=new_card.id)

        except Exception as e:
            logger.error(f"后台重写任务失败", card_id=card_id, error=str(e), exc_info=True)
            # 这里可以添加错误处理逻辑，比如更新任务状态等


class LogicTreeTrendingHandler(BaseHandler):
    """trending处理器"""

    @inject
    def initialize(
            self,
            logic_tree_service: LogicTreeService = Provide[Container.logic_tree_service],
            github_trending_service: GitHubTrendingService = Provide[Container.github_trending_service],
    ):
        """初始化处理器"""
        self.logic_tree_service = logic_tree_service
        super().initialize()

    async def get(self) -> None:
        """当前趋势"""
        try:
            # get from tmp
            trending_result = await self.logic_tree_service.get_daily_trending_projects()
            self.success_response(data=trending_result)


        except ValueError as e:
            logger.error("重写卡片参数错误", error=str(e))
            self.write_error(500, error_message=f"重写卡片参数错误: {str(e)}")
        except Exception as e:
            logger.error("重写卡片失败", error=str(e))
            self.write_error(500, error_message=f"重写卡片失败: {str(e)}")


class LogicTreeAssociatedProjectsHandler(BaseHandler):
    """获取关联项目"""

    @inject
    def initialize(
            self,
            logic_tree_service: LogicTreeService = Provide[Container.logic_tree_service]
    ):
        """初始化处理器"""
        self.logic_tree_service = logic_tree_service
        super().initialize()

    async def post(self) -> None:
        """重写卡片"""
        try:
            # 获取当前用户ID
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)

            # 验证请求数据
            if not self.json_body:
                raise ValueError("请求体不能为空")

            # 获取参数
            project_id = self.json_body.get("project_id")
            if not project_id:
                raise ValueError("project_id不能为空")
            # 调用服务获取相关列表
            project_list = await self.logic_tree_service.get_similar_list(
                project_id=project_id
            )

            # 返回结果
            self.success_response(project_list)

        except ValueError as e:
            logger.error("获取list", error=str(e))
            self.write_error(500, error_message=f"重写卡片参数错误: {str(e)}")
        except Exception as e:
            logger.error("重写卡片失败", error=str(e))
            self.write_error(500, error_message=f"重写卡片失败: {str(e)}")
