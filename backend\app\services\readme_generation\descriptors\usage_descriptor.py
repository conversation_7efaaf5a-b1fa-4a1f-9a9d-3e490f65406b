"""
使用说明生成描述器

通过提示词指导LLM生成项目使用方法和示例代码
"""

import logging
from typing import List

from ...execution_framework.base.step_type import StepType
from ...execution_framework.models.context import ExecutionContext
from ...execution_framework.models.tool_instruction import ToolInstruction
from .base_generation_descriptor import BaseGenerationDescriptor

logger = logging.getLogger(__name__)


class UsageDescriptor(BaseGenerationDescriptor):
    """项目使用说明生成描述器 - 指导LLM生成使用方法和示例"""

    def __init__(self):
        """初始化使用说明生成描述器"""
        super().__init__(StepType.USAGE, "usage")

    def get_description(self) -> str:
        """获取描述器描述"""
        return "指导LLM生成项目使用方法、示例代码和最佳实践"

    def get_tool_instructions(self, context: ExecutionContext) -> List[ToolInstruction]:
        """获取工具调用指令列表 - 每个工具只定义一次"""
        return [
            ToolInstruction(
                name="codebase-retrieval",
                parameters={
                    "information_request": "项目主要API接口、使用示例、配置选项、命令行工具、核心类和方法"
                },
                description="获取项目使用相关信息",
                result_key="codebase_analysis"
            ),
            ToolInstruction(
                name="view-directory",
                parameters={"path": "examples"},
                description="查看项目目录结构（可多次调用查看不同目录）",
                result_key="directory_info"
            )
        ]

    def get_prompt_template(self) -> str:
        """获取提示词模板 - 指导LLM如何生成使用说明"""
        return """
## 项目信息
项目名称: {project_name}
项目路径: {project_path}
项目类型: {project_type}
编程语言: {programming_languages}
技术框架: {frameworks}

## 代码库分析
{codebase_analysis}

## 目录信息
{directory_info}

## 自定义要求
{custom_prompt}

## 生成任务

请基于以上项目信息，使用可用工具深入分析后，生成详细的使用说明。

### 工具使用指导：
1. 使用 codebase-retrieval 获取项目API接口和使用示例
2. 使用 view-directory 查看多个相关目录：
   - examples/（示例代码）
   - docs/（文档目录）
   - src/（源代码目录）
   - tests/（测试用例，了解使用方式）

### 重点包含：
1. 基本使用方法和核心API
2. 完整的代码示例（从简单到复杂）
3. 配置选项和参数说明
4. 常见使用场景和最佳实践
5. 注意事项和限制说明
"""

    def get_input_variables(self) -> List[str]:
        """获取输入变量列表"""
        return [
            "project_name",
            "project_path",
            "project_type",
            "programming_languages",
            "frameworks",
            "codebase_analysis",
            "directory_info",
            "custom_prompt"
        ]

    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return "你是一个专业的技术文档工程师和开发者，擅长编写清晰实用的使用指南和示例代码。"

    def get_constraints(self) -> List[str]:
        """将写作要求迁移为约束条件"""
        return [
            "使用Markdown格式",
            "提供完整可运行的代码示例",
            "内容必须基于实际项目代码和API",
            "输出包含：基本用法、代码示例、配置说明、使用场景、注意事项",
            "代码示例要有注释说明",
            "从简单示例逐步到复杂应用",
            "总长度控制在1000-1500字",
        ]

    def get_priority(self, context: ExecutionContext) -> int:
        """获取优先级 - 使用说明优先级较高"""
        return 85

    def get_dependencies(self) -> List[str]:
        """获取依赖 - 依赖安装说明"""
        return ["installation"]
