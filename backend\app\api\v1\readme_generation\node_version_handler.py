"""
节点版本管理处理器
"""

import json
from typing import Dict, Any, Optional
from tornado.web import HTTPError
import structlog
from dependency_injector.wiring import inject, Provide

from app.api.base import BaseHandler
from app.core.di.containers import Container
from app.services.readme_generation.generation_manager import GenerationManager

logger = structlog.get_logger(__name__)


class NodeVersionHandler(BaseHandler):
    """节点版本管理处理器"""

    @inject
    def initialize(
        self,
        readme_generation_manager: GenerationManager = Provide[Container.readme_generation_manager],
    ):
        super().initialize()
        self.readme_generation_manager = readme_generation_manager

    async def get(self, node_id: str):
        """获取节点版本历史"""
        try:
            history = await self.readme_generation_manager.get_node_version_history(node_id)
            self.success_response({"history": history}, "版本历史获取成功")

        except Exception as e:
            logger.error("获取版本历史失败", error=str(e), exc_info=e)
            raise HTTPError(500, f"获取版本历史失败: {str(e)}")

    async def put(self, node_id: str):
        """切换节点版本或设置质量评分"""
        try:
            if not self.json_body:
                raise HTTPError(400, "请求体不能为空")

            action = self.json_body.get("action")
            version_id = self.json_body.get("version_id")

            if not action or not version_id:
                raise HTTPError(400, "action和version_id参数是必需的")

            if action == "switch":
                success = await self.readme_generation_manager.switch_node_version(node_id, version_id)
                message = "版本切换成功" if success else "版本切换失败"
                
            elif action == "score":
                score = self.json_body.get("score")
                feedback = self.json_body.get("feedback", "")
                
                if score is None:
                    raise HTTPError(400, "score参数是必需的")
                
                success = await self.readme_generation_manager.set_version_quality_score(
                    node_id, version_id, score, feedback
                )
                message = "质量评分设置成功" if success else "质量评分设置失败"

            else:
                raise HTTPError(400, f"不支持的操作: {action}")

            self.success_response({"success": success}, message)

        except HTTPError:
            raise
        except Exception as e:
            logger.error("版本操作失败", error=str(e), exc_info=e)
            raise HTTPError(500, f"版本操作失败: {str(e)}")

    async def delete(self, node_id: str):
        """删除节点版本"""
        try:
            version_id = self.get_argument("version_id", None)
            if not version_id:
                raise HTTPError(400, "version_id参数是必需的")

            success = await self.readme_generation_manager.delete_node_version(node_id, version_id)
            message = "版本删除成功" if success else "版本删除失败"
            
            self.success_response({"success": success}, message)
            
        except HTTPError:
            raise
        except Exception as e:
            logger.error("删除版本失败", error=str(e), exc_info=e)
            raise HTTPError(500, f"删除版本失败: {str(e)}")
